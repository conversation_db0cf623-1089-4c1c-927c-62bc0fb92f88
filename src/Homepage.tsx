import React, { useState } from 'react';
import { Zap, Shield, Leaf, Users, Phone, Mail } from 'lucide-react';
import logoImage from '../../assets/logo.png';
import edwinImage from '../../assets/edwin.png';
import realEdwinImage from '../../assets/real-edwin.png';

const Homepage: React.FC = () => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  const handleEdwinClick = () => {
    if (isAnimating) return; // Prevent multiple clicks during animation

    setIsAnimating(true);
    setIsFlipped(!isFlipped);

    // Reset animation state after animation completes
    setTimeout(() => {
      setIsAnimating(false);
    }, 600); // Match the CSS animation duration
  };

  return (
    <div className="homepage">
      {/* Header */}
      <header className="homepage__header">
        <div className="homepage__header-container">
          <div className="homepage__logo">
            <img
              src={logoImage}
              alt="Company Logo"
              className="homepage__logo-image"
            />
          </div>
          <nav className="homepage__nav">
            <a href="#" className="homepage__nav-link">Stroom & Gas</a>
            <a href="#" className="homepage__nav-link">Zonnepanelen</a>
            <a href="#" className="homepage__nav-link">Product & Advies</a>
            <a href="#" className="homepage__nav-link">Klantenservice</a>
          </nav>
          <div className="homepage__header-actions">
            <button className="homepage__login-btn">Mijn Eneco</button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="homepage__hero">
        <div className="homepage__hero-container">
          <div className="homepage__hero-content">
            <div className="homepage__hero-text">
              <h1 className="homepage__hero-title">
                Kies een energiecontract dat bij je past
              </h1>
              <ul className="homepage__hero-features">
                <li>✓ Altijd groene stroom uit Nederland</li>
                <li>✓ Altijd grip op je verbruik met de Eneco app</li>
                <li>✓ Altijd een seintje als je termijnbedrag afwijkt van je voorschot</li>
              </ul>
              <div className="homepage__hero-form">
                <div className="homepage__form-row">
                  <select className="homepage__form-select">
                    <option>Postcode</option>
                  </select>
                  <select className="homepage__form-select">
                    <option>Huisnummer</option>
                  </select>
                  <select className="homepage__form-select">
                    <option>Toevoeging</option>
                  </select>
                </div>
                <div className="homepage__form-actions">
                  <button className="homepage__btn homepage__btn--primary">
                    Vergelijk en maak keuze
                  </button>
                  <button className="homepage__btn homepage__btn--secondary">
                    Contact opnemen
                  </button>
                </div>
              </div>
              <p className="homepage__hero-note">
                Direct 3 jaar voordeel - Geen gedoe met overstappen
              </p>
            </div>
            <div className="homepage__hero-image">
              <div
                className={`homepage__edwin-container ${isFlipped ? 'homepage__edwin-container--flipped' : ''} ${isAnimating ? 'homepage__edwin-container--animating' : ''}`}
                onClick={handleEdwinClick}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleEdwinClick();
                  }
                }}
                aria-label={isFlipped ? "Edwin revealed - Click to see normal Edwin" : "Edwin - Click to reveal the real Edwin"}
              >
                <div className="homepage__edwin-card">
                  <div className="homepage__edwin-card-front">
                    <img
                      src={edwinImage}
                      alt="Edwin - Eneco Energy Advisor"
                      className="homepage__edwin-image"
                    />
                  </div>
                  <div className="homepage__edwin-card-back">
                    <img
                      src={realEdwinImage}
                      alt="Real Edwin - The true Eneco Energy Advisor"
                      className="homepage__edwin-image"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="homepage__services">
        <div className="homepage__services-container">
          <div className="homepage__service-item">
            <Zap className="homepage__service-icon" />
            <span className="homepage__service-text">Energiecontract</span>
          </div>
          <div className="homepage__service-item">
            <Shield className="homepage__service-icon" />
            <span className="homepage__service-text">Veiligheid en service</span>
          </div>
          <div className="homepage__service-item">
            <Leaf className="homepage__service-icon" />
            <span className="homepage__service-text">Duurzaamheid</span>
          </div>
          <div className="homepage__service-item">
            <Users className="homepage__service-icon" />
            <span className="homepage__service-text">Klantenservice</span>
          </div>
          <div className="homepage__service-item">
            <Phone className="homepage__service-icon" />
            <span className="homepage__service-text">Storing</span>
          </div>
          <div className="homepage__service-item">
            <Mail className="homepage__service-icon" />
            <span className="homepage__service-text">Mijn Eneco</span>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="homepage__benefits">
        <div className="homepage__benefits-container">
          <h2 className="homepage__benefits-title">De voordelen van Eneco</h2>
          <div className="homepage__benefits-grid">
            <div className="homepage__benefit-card">
              <div className="homepage__benefit-icon">
                <Leaf />
              </div>
              <div className="homepage__benefit-content">
                <h3>Altijd 100% groene stroom</h3>
                <p>
                  O Eneco 100% groene stroom betekent dat wij voor elke kWh die je verbruikt,
                  een kWh groene stroom opwekken of inkopen.
                </p>
              </div>
            </div>
            <div className="homepage__benefit-card">
              <div className="homepage__benefit-icon">
                <Zap />
              </div>
              <div className="homepage__benefit-content">
                <h3>Een de voordeligste van de Benelux</h3>
                <p>
                  Eneco verkoopt de voordeligste energie van de Benelux.
                  Wij zorgen ervoor dat je altijd de beste prijs krijgt.
                </p>
              </div>
            </div>
            <div className="homepage__benefit-card">
              <div className="homepage__benefit-icon">
                <Shield />
              </div>
              <div className="homepage__benefit-content">
                <h3>Goede van start tot eind energie</h3>
                <p>
                  Wij zijn er voor je, van het moment dat je klant wordt tot aan
                  het einde van je contract. Altijd bereikbaar.
                </p>
              </div>
            </div>
            <div className="homepage__benefit-card">
              <div className="homepage__benefit-icon">
                <Users />
              </div>
              <div className="homepage__benefit-content">
                <h3>Onbeperkt en gratis klantenservice</h3>
                <p>
                  Heb je vragen over je contract of factuur?
                  Neem gratis contact op met onze klantenservice.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Homepage;
