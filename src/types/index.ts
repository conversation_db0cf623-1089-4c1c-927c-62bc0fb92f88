// Export all types from the types directory

// Theme types
export * from './theme';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type Nullable<T> = T | null;

export type Maybe<T> = T | undefined;

export type NonEmptyArray<T> = [T, ...T[]];

export type ValueOf<T> = T[keyof T];

export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

// Event handler types
export type EventHandler<T = Event> = (event: T) => void;

export type ChangeEventHandler<T = HTMLInputElement> = EventHandler<React.ChangeEvent<T>>;

export type ClickEventHandler<T = HTMLElement> = EventHandler<React.MouseEvent<T>>;

export type KeyboardEventHandler<T = HTMLElement> = EventHandler<React.KeyboardEvent<T>>;

export type FormEventHandler<T = HTMLFormElement> = EventHandler<React.FormEvent<T>>;

// Component types
export type ComponentSize = 'sm' | 'md' | 'lg';

export type ComponentVariant = 'primary' | 'secondary' | 'ghost' | 'danger';

export type ComponentState = 'default' | 'loading' | 'disabled' | 'error';

// Layout types
export type FlexDirection = 'row' | 'column' | 'row-reverse' | 'column-reverse';

export type FlexAlign = 'start' | 'center' | 'end' | 'stretch' | 'baseline';

export type FlexJustify = 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';

// Responsive types
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl';

export type ResponsiveValue<T> = T | Partial<Record<Breakpoint, T>>;

// Animation types
export type AnimationDuration = 'fast' | 'normal' | 'slow';

export type AnimationEasing = 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';

// Color types
export type ColorScheme = 'light' | 'dark';

export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'neutral';

// Status types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type AsyncState<T, E = Error> = {
  data: T | null;
  loading: boolean;
  error: E | null;
};

// Form types
export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'tel' 
  | 'url' 
  | 'search' 
  | 'textarea' 
  | 'select' 
  | 'checkbox' 
  | 'radio';

export interface FormField {
  name: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: unknown) => string | undefined;
  };
}

export interface FormError {
  field: string;
  message: string;
}

export type FormState<T> = {
  values: T;
  errors: FormError[];
  touched: Record<keyof T, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
};

// Storage types
export type StorageType = 'localStorage' | 'sessionStorage' | 'memory';

export interface StorageAdapter {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

// Environment types
export type Environment = 'development' | 'staging' | 'production';

export interface EnvironmentConfig {
  apiUrl: string;
  wsUrl: string;
  environment: Environment;
  debug: boolean;
  version: string;
}

// Feature flag types
export type FeatureFlag = 
  | 'enableWebSocket' 
  | 'enableNotifications' 
  | 'enablePersistence' 
  | 'enableAnalytics'
  | 'enableDarkMode'
  | 'enableFloatingChat';

export type FeatureFlags = Record<FeatureFlag, boolean>;

// Analytics types
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, unknown>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export type AnalyticsEventName = 
  | 'chat_opened'
  | 'chat_closed'
  | 'message_sent'
  | 'message_received'
  | 'connection_failed'
  | 'theme_changed'
  | 'view_mode_changed';

// Performance types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count';
  timestamp: Date;
}

export type PerformanceMetricName = 
  | 'message_send_time'
  | 'message_receive_time'
  | 'connection_time'
  | 'render_time'
  | 'bundle_size';

// Accessibility types
export type AriaRole = 
  | 'button'
  | 'dialog'
  | 'textbox'
  | 'listbox'
  | 'option'
  | 'menu'
  | 'menuitem'
  | 'tab'
  | 'tabpanel'
  | 'alert'
  | 'status'
  | 'log';

export interface AriaAttributes {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-disabled'?: boolean;
  'aria-selected'?: boolean;
  'aria-checked'?: boolean;
  'aria-pressed'?: boolean;
  'aria-current'?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  'aria-live'?: 'off' | 'polite' | 'assertive';
  'aria-atomic'?: boolean;
  'aria-busy'?: boolean;
  'aria-controls'?: string;
  'aria-owns'?: string;
  'aria-activedescendant'?: string;
  role?: AriaRole;
}
