// Theme types for the homepage project

export type ColorScheme = 'light' | 'dark';

export type ThemeMode = 'light' | 'dark' | 'system';

// Theme mode constants
export const THEME_MODE = {
  LIGHT: 'light' as const,
  DARK: 'dark' as const,
  SYSTEM: 'system' as const,
} as const;

// Basic theme interface for homepage
export interface Theme {
  scheme: ColorScheme;
}

// Theme configuration interface
export interface ThemeConfig {
  light: Theme;
  dark: Theme;
}

// Theme context type (for React context)
export interface ThemeContextType {
  theme: Theme;
  colorScheme: ColorScheme;
  toggleTheme: () => void;
  setTheme: (scheme: ColorScheme) => void;
}
