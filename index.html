<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Eneco</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Eneco Chatbot Widget -->
    <link rel="stylesheet" href="/widgets/style.css">
    <script src="/widgets/eneco-chatbot-widget.iife.js"></script>
    <script>
      // Initialize the chatbot widget
      if (window.EnecoChat) {
        window.EnecoChat.init({
          apiEndpoint: 'https://localhost:55018',
          customerId: '********',
          accountId: '2',
          theme: 'light',
          position: 'bottom-right',
          initialMode: 'minimized',
          streamingEnabled: true,
          debugMode: false
        });
      }
    </script>
  </body>
</html>
