<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eneco Chatbot Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .content {
            line-height: 1.6;
            color: #666;
        }
        .test-buttons {
            margin-top: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Eneco Chatbot Widget Test Page</h1>
        <div class="content">
            <p>This is a test page to verify that the Eneco chatbot widget works correctly as a standalone component.</p>
            <p>The chatbot should appear as a floating button in the bottom-right corner of the page.</p>
            <p>You can interact with it independently of this page content.</p>
            
            <div class="test-buttons">
                <button onclick="testShowWidget()">Show Widget</button>
                <button onclick="testHideWidget()">Hide Widget</button>
                <button onclick="testSendMessage()">Send Test Message</button>
                <button onclick="testClearChat()">Clear Chat</button>
                <button onclick="testGetConfig()">Get Config</button>
            </div>
        </div>
    </div>

    <!-- Eneco Chatbot Widget -->
    <link rel="stylesheet" href="./public/widgets/style.css">
    <script src="./public/widgets/eneco-chatbot-widget.iife.js"></script>
    <script>
        // Initialize the chatbot widget
        if (window.EnecoChat) {
            console.log('Initializing Eneco Chat Widget...');
            window.EnecoChat.init({
                apiEndpoint: 'https://localhost:55018',
                customerId: '********',
                accountId: '2',
                theme: 'light',
                position: 'bottom-right',
                initialMode: 'minimized',
                streamingEnabled: true,
                debugMode: true
            });

            // Set up event listeners
            window.EnecoChat.on('show', () => console.log('Widget shown'));
            window.EnecoChat.on('hide', () => console.log('Widget hidden'));
            window.EnecoChat.on('sendMessage', (data) => console.log('Message sent:', data));
            window.EnecoChat.on('clearChat', () => console.log('Chat cleared'));
        } else {
            console.error('EnecoChat widget not found!');
        }

        // Test functions
        function testShowWidget() {
            if (window.EnecoChat) {
                window.EnecoChat.show();
            }
        }

        function testHideWidget() {
            if (window.EnecoChat) {
                window.EnecoChat.hide();
            }
        }

        function testSendMessage() {
            if (window.EnecoChat) {
                window.EnecoChat.sendMessage('Hello from test page!');
            }
        }

        function testClearChat() {
            if (window.EnecoChat) {
                window.EnecoChat.clearChat();
            }
        }

        function testGetConfig() {
            if (window.EnecoChat) {
                console.log('Current config:', window.EnecoChat.getConfig());
            }
        }
    </script>
</body>
</html>
