# Eneco Chatbot Widget

A standalone chatbot widget that can be embedded into any website via a simple script tag. Built with React, TypeScript, and Vite, bundled as a self-contained IIFE module with Shadow DOM isolation.

## Features

- 🚀 **Easy Integration**: Single script tag deployment
- 🎨 **Style Isolation**: Shadow DOM prevents CSS conflicts
- 📱 **Responsive Design**: Works on desktop and mobile
- 🔄 **Real-time Streaming**: Live chat responses with typing indicators
- 🎯 **3-State UI**: Fullpage, chatbox, and minimized modes
- 🌙 **Theme Support**: Light and dark themes
- 🔧 **Configurable**: Extensive configuration options
- ♿ **Accessible**: WCAG compliant interface

## Quick Start

### 1. Include the Script

```html
<script src="./eneco-chatbot-widget.iife.js"></script>
```

### 2. Initialize the Widget

```javascript
EnecoChat.init({
  apiEndpoint: 'https://your-api-endpoint.com/chat',
  customerId: 'your-customer-id',
  accountId: 'your-account-id'
});
```

## Configuration Options

```javascript
EnecoChat.init({
  // Required
  apiEndpoint: 'https://api.example.com/chat',
  customerId: 'customer-123',
  
  // Optional
  accountId: 'account-456',
  containerId: 'eneco-chat-root', // Custom container ID
  theme: 'light', // 'light' | 'dark' | 'auto'
  position: 'bottom-right', // 'bottom-right' | 'bottom-left'
  initialMode: 'minimized', // 'minimized' | 'chatbox' | 'fullpage'
  streamingEnabled: true,
  
  // Styling
  primaryColor: '#E5384C',
  fontFamily: 'Etelka, Inter, sans-serif',
  
  // Behavior
  autoOpen: false,
  showUnreadCount: true,
  enableSounds: false
});
```

## API Methods

```javascript
// Show/hide the widget
EnecoChat.show();
EnecoChat.hide();

// Change view mode
EnecoChat.setMode('fullpage'); // 'fullpage' | 'chatbox' | 'minimized'

// Send a message programmatically
EnecoChat.sendMessage('Hello from the host page!');

// Clear chat history
EnecoChat.clearChat();

// Update configuration
EnecoChat.updateConfig({
  theme: 'dark',
  primaryColor: '#007250'
});

// Destroy the widget
EnecoChat.destroy();
```

## Events

```javascript
// Listen to widget events
EnecoChat.on('ready', () => {
  console.log('Widget is ready');
});

EnecoChat.on('message', (data) => {
  console.log('New message:', data);
});

EnecoChat.on('modeChange', (mode) => {
  console.log('Mode changed to:', mode);
});
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License - see LICENSE file for details.
