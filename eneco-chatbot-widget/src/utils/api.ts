import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import axios from 'axios';
import {
  type ApiResponse,
  type ApiError,
  type HttpClientConfig,
  DEFAULT_API_CONFIG,
  API_ERROR_CODES,
  type Api<PERSON>rrorCode,
} from '../types/api';

// Create axios instance with default configuration
const createApiClient = (config: Partial<HttpClientConfig> = {}): AxiosInstance => {
  const finalConfig = { ...DEFAULT_API_CONFIG, ...config };
  
  const client = axios.create({
    baseURL: finalConfig.baseURL,
    timeout: finalConfig.timeout,
    headers: finalConfig.headers,
  });
  
  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add timestamp to requests (store in headers for tracking)
      config.headers['X-Request-Start-Time'] = Date.now().toString();

      // Add request ID for tracking
      config.headers['X-Request-ID'] = generateRequestId();

      // Log request in development
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Calculate request duration
      const startTime = parseInt(response.config.headers['X-Request-Start-Time'] as string || '0');
      const duration = Date.now() - startTime;
      
      // Log response in development
      console.log(`✅ API Response: ${response.status} ${response.config.url} (${duration}ms)`);
      
      // Transform response to our standard format
      const apiResponse: ApiResponse = {
        data: response.data,
        success: true,
        message: response.statusText,
        timestamp: new Date().toISOString(),
      };
      
      return { ...response, data: apiResponse };
    },
    (error: AxiosError) => {
      // Calculate request duration
      const startTime = parseInt(error.config?.headers?.['X-Request-Start-Time'] as string || '0');
      const duration = Date.now() - startTime;
      
      // Log error in development
      console.error(`❌ API Error: ${error.response?.status || 'Network'} ${error.config?.url} (${duration}ms)`);
      
      // Transform error to our standard format
      const apiError = transformAxiosError(error);
      return Promise.reject(apiError);
    }
  );
  
  return client;
};

// Transform Axios error to our ApiError format
const transformAxiosError = (error: AxiosError): ApiError => {
  let code: ApiErrorCode;
  let message: string;
  let details: unknown;

  if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    
    switch (status) {
      case 400:
        code = API_ERROR_CODES.VALIDATION_ERROR;
        message = 'Invalid request data';
        break;
      case 401:
        code = API_ERROR_CODES.UNAUTHORIZED;
        message = 'Authentication required';
        break;
      case 403:
        code = API_ERROR_CODES.FORBIDDEN;
        message = 'Access denied';
        break;
      case 404:
        code = API_ERROR_CODES.NOT_FOUND;
        message = 'Resource not found';
        break;
      case 429:
        code = API_ERROR_CODES.RATE_LIMITED;
        message = 'Too many requests';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        code = API_ERROR_CODES.SERVER_ERROR;
        message = 'Server error';
        break;
      default:
        code = API_ERROR_CODES.SERVER_ERROR;
        message = `HTTP ${status} error`;
    }
    
    details = error.response.data;
  } else if (error.request) {
    // Network error
    code = API_ERROR_CODES.NETWORK_ERROR;
    message = 'Network connection failed';
    details = error.message;
  } else {
    // Request setup error
    code = API_ERROR_CODES.NETWORK_ERROR;
    message = error.message || 'Request failed';
    details = error;
  }

  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
};

// Generate unique request ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Retry configuration
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: ApiError) => boolean;
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  retries: 2,
  retryDelay: 1000,
  retryCondition: (error: ApiError) => {
    // Retry on network errors and 5xx server errors
    return error.code === API_ERROR_CODES.NETWORK_ERROR || 
           error.code === API_ERROR_CODES.SERVER_ERROR ||
           error.code === API_ERROR_CODES.SERVICE_UNAVAILABLE;
  },
};

// Create API client with retry logic
const createApiClientWithRetry = (
  config: Partial<HttpClientConfig> = {},
  retryConfig: Partial<RetryConfig> = {}
): AxiosInstance => {
  const client = createApiClient(config);
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  // Add retry interceptor
  client.interceptors.response.use(
    (response) => response,
    async (error: ApiError) => {
      const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean; _retryCount?: number };
      
      if (
        !originalRequest._retry &&
        finalRetryConfig.retryCondition?.(error) &&
        (originalRequest._retryCount || 0) < finalRetryConfig.retries
      ) {
        originalRequest._retry = true;
        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, finalRetryConfig.retryDelay));
        
        console.log(`🔄 Retrying request (attempt ${originalRequest._retryCount}/${finalRetryConfig.retries})`);
        
        return client(originalRequest);
      }
      
      return Promise.reject(error);
    }
  );

  return client;
};

// Create configurable API client factory
export const createConfigurableApiClient = (baseURL: string) => {
  return createApiClientWithRetry({ baseURL });
};

// Create default API client instances
export const apiClient = createApiClient();
export const apiClientWithRetry = createApiClientWithRetry();

// Export factory functions
export { createApiClient, createApiClientWithRetry };

// Export utility functions
export { transformAxiosError, generateRequestId };
