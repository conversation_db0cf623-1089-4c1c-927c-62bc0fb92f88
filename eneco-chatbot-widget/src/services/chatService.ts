import {
  type ChatService,
  type SendMessagePayload,
  type SendMessageApiResponse,
} from '../types/api';
import { createConfigurableApiClient } from '../utils/api';
import { streamingService } from './streamingService';

// Chat API endpoints - simplified for basic message sending
const ENDPOINTS = {
  SEND_MESSAGE: '/chat',
} as const;

// Implementation of ChatService
class ChatServiceImpl implements ChatService {
  private apiClient: ReturnType<typeof createConfigurableApiClient>;

  constructor(baseURL: string) {
    this.apiClient = createConfigurableApiClient(baseURL);
  }

  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    console.log('Sending message payload:', payload);

    const response = await this.apiClient.post<SendMessageApiResponse>(
      ENDPOINTS.SEND_MESSAGE,
      payload,
      {},
    );
    // The API client wraps responses in { data, success, message, timestamp }
    // So the actual API response is in response.data
    return response.data as SendMessageApiResponse;
  }

  async sendStreamingMessage(payload: SendMessagePayload): Promise<Response> {
    console.log('Sending streaming message payload:', payload);
    return streamingService.handleStreamingMessage(payload, this.apiClient.defaults.baseURL);
  }
}

// Service factory
export const createChatService = (baseURL: string): ChatService => {
  return new ChatServiceImpl(baseURL);
};

// Mock service for development/testing
class MockChatService implements ChatService {
  
  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const conversationId = payload.conversationId || 'default_conversation';
    
    const response = "Mock service is disabled. Using real API endpoint.";

    return {
      messageId,
      message: response, // Use 'message' field to match API response structure
      threadId: conversationId, // Use threadId field
      timestamp: new Date().toISOString(),
      metadata: {
        processingTime: Math.random() * 2000,
        confidence: 0.8 + Math.random() * 0.2,
      },
    };
  }

  async sendStreamingMessage(payload: SendMessagePayload): Promise<Response> {
    // Mock streaming response
    const mockResponse = new Response(
      new ReadableStream({
        start(controller) {
          const chunks = [
            '[STARTED THREAD]: mock_thread_123',
            '[TOOLCALL] mock-agent [ENDTOOLCALL]',
            '[STARTED RESPONSE]: ',
            'This is a mock streaming response. ',
            'It simulates how the real API would work. ',
            'Each chunk arrives with a small delay.'
          ];
          
          let index = 0;
          const sendChunk = () => {
            if (index < chunks.length) {
              controller.enqueue(new TextEncoder().encode(chunks[index]));
              index++;
              setTimeout(sendChunk, 100);
            } else {
              controller.close();
            }
          };
          
          setTimeout(sendChunk, 100);
        }
      }),
      {
        headers: {
          'Content-Type': 'text/plain',
        },
      }
    );
    
    return mockResponse;
  }
}

// Export mock service for development
export const mockChatService = new MockChatService();

// Default export - create service with default URL
export default createChatService('https://localhost:55018');
