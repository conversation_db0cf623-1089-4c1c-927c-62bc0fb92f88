import {
  type StreamingService,
  type StreamingChunk,
  type SendMessagePayload,
  type StreamingConfig,
  DEFAULT_API_CONFIG,
} from '../types/api';

// Default streaming configuration
const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  enabled: true,
  chunkSize: 1024,
  timeout: 1000000,
  retryAttempts: 0,
  retryDelay: 100000,
};

// Streaming throttling configuration
interface StreamingThrottleConfig {
  chunkDelay: number; // Delay between processing chunks (ms)
  characterDelay: number; // Delay between characters for typing effect (ms)
  toolCallDisplayTime: number; // How long to show tool call info (ms)
  agentDisplayTime: number; // How long to show agent info (ms)
  enabled: boolean; // Whether throttling is enabled
}

const DEFAULT_THROTTLE_CONFIG: StreamingThrottleConfig = {
  chunkDelay: 100, // Default values for widget
  characterDelay: 25,
  toolCallDisplayTime: 2000,
  agentDisplayTime: 1500,
  enabled: true, // Default to enabled
};

// Implementation of StreamingService
class StreamingServiceImpl implements StreamingService {
  private config: StreamingConfig;
  private throttleConfig: StreamingThrottleConfig;

  constructor(config: Partial<StreamingConfig> = {}) {
    this.config = { ...DEFAULT_STREAMING_CONFIG, ...config };
    this.throttleConfig = { ...DEFAULT_THROTTLE_CONFIG };
  }

  async handleStreamingMessage(payload: SendMessagePayload): Promise<Response> {
    console.log('🚀 Using real streaming API:', DEFAULT_API_CONFIG.baseURL);
    const endpoint = `${DEFAULT_API_CONFIG.baseURL}/chat`;

    // Create an AbortController for better control over the request
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      abortController.abort('Request timeout');
    }, this.config.timeout || 30000);

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/plain',
        },
        body: JSON.stringify({
          ...payload,
          stream: true, // Enable streaming
        }),
        signal: abortController.signal,
      });

      // Clear the timeout since the request succeeded
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      if (!response.body) {
        throw new Error('No response body for streaming');
      }

      return response;
    } catch (error) {
      // Clear timeout on error
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Streaming request timed out');
        }
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          throw new Error('Network error - please check your connection');
        }
      }

      console.error('Streaming request failed:', error);
      throw error;
    }
  }

  async *processStreamingResponse(response: Response): AsyncGenerator<StreamingChunk, void, unknown> {
    if (!response.body) {
      throw new Error('No response body available for streaming');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // Process any remaining content in buffer
          if (buffer.trim()) {
            yield* this.parseStreamingChunks(buffer);
          }
          break;
        }

        // Decode the chunk and add to buffer
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // Process complete chunks (separated by newlines or specific delimiters)
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep the last incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            yield* this.parseStreamingChunks(line);
            
            // Add throttling delay if enabled
            if (this.throttleConfig.enabled && this.throttleConfig.chunkDelay > 0) {
              await this.delay(this.throttleConfig.chunkDelay);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error processing streaming response:', error);
      throw error;
    } finally {
      reader.releaseLock();
    }
  }

  private async *parseStreamingChunks(content: string): AsyncGenerator<StreamingChunk, void, unknown> {
    // Parse different types of streaming content based on the API format
    // Format: [STARTED THREAD]: thread_id[TOOLCALL] agent-name [ENDTOOLCALL][STARTED RESPONSE]: response_text

    if (content.includes('[STARTED THREAD]:')) {
      const threadMatch = content.match(/\[STARTED THREAD\]:\s*(.+?)(?:\[|$)/);
      if (threadMatch) {
        yield {
          type: 'thread_id',
          content: threadMatch[1].trim(),
        };
      }
    }

    if (content.includes('[TOOLCALL]') && content.includes('[ENDTOOLCALL]')) {
      const toolMatch = content.match(/\[TOOLCALL\]\s*(.+?)\s*\[ENDTOOLCALL\]/);
      if (toolMatch) {
        yield {
          type: 'tool_call',
          content: toolMatch[1].trim(),
        };
        
        // Add display delay for tool calls
        if (this.throttleConfig.enabled && this.throttleConfig.toolCallDisplayTime > 0) {
          await this.delay(this.throttleConfig.toolCallDisplayTime);
        }
      }
    }

    if (content.includes('[STARTED RESPONSE]:')) {
      const responseMatch = content.match(/\[STARTED RESPONSE\]:\s*(.*)$/);
      if (responseMatch) {
        yield {
          type: 'response_start',
          content: '',
        };
        
        const responseText = responseMatch[1];
        if (responseText) {
          // Process response text with character-by-character throttling if enabled
          if (this.throttleConfig.enabled && this.throttleConfig.characterDelay > 0) {
            yield* this.processTextWithThrottling(responseText);
          } else {
            yield {
              type: 'text',
              content: responseText,
            };
          }
        }
      }
    } else if (!content.includes('[') && !content.includes(']')) {
      // Regular text content (not a control message)
      if (this.throttleConfig.enabled && this.throttleConfig.characterDelay > 0) {
        yield* this.processTextWithThrottling(content);
      } else {
        yield {
          type: 'text',
          content: content,
        };
      }
    }
  }

  private async *processTextWithThrottling(text: string): AsyncGenerator<StreamingChunk, void, unknown> {
    // Split text into words for more natural streaming
    const words = text.split(' ');
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const isLastWord = i === words.length - 1;
      const content = isLastWord ? word : word + ' ';
      
      yield {
        type: 'text',
        content: content,
      };
      
      // Add delay between words for natural typing effect
      if (!isLastWord && this.throttleConfig.characterDelay > 0) {
        await this.delay(this.throttleConfig.characterDelay);
      }
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Update throttle configuration
  updateThrottleConfig(config: Partial<StreamingThrottleConfig>): void {
    this.throttleConfig = { ...this.throttleConfig, ...config };
  }

  // Get current throttle configuration
  getThrottleConfig(): StreamingThrottleConfig {
    return { ...this.throttleConfig };
  }
}

// Export singleton instance
export const streamingService = new StreamingServiceImpl();

// Export for testing and configuration
export { StreamingServiceImpl, DEFAULT_STREAMING_CONFIG, DEFAULT_THROTTLE_CONFIG };
