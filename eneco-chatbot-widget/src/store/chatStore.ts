import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ViewMode, SimpleMessage, ChatStore } from '../types/chat';
import { VIEW_MODE } from '../types/chat';

// Generate unique ID for messages
const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Create the simplified chat store
export const useChatStore = create<ChatStore>()(
  devtools(
    (set) => ({
        // Initial state
        messages: [],
        isTyping: false,
        viewMode: VIEW_MODE.MINIMIZED,
        unreadCount: 0,
        currentThreadId: null,
        isChatboxExpanded: false,
        streamingEnabled: true, 
        currentStreamingMessageId: null,
        currentAgent: null,

        // Actions
        addMessage: (content: string, sender: 'user' | 'agent', images?: string[]) => {
          const message: SimpleMessage = {
            id: generateMessageId(),
            content,
            sender,
            timestamp: new Date(),
            ...(images && images.length > 0 && { images }),
          };

          // console.log('Adding message to store:', { content, sender });

          set((state) => ({
            messages: [...state.messages, message],
            // Increment unread count if it's an agent message and chat is minimized
            unreadCount:
              sender === 'agent' && state.viewMode === VIEW_MODE.MINIMIZED
                ? state.unreadCount + 1
                : state.unreadCount,
          }));
        },

        updateMessage: (id: string, updates: Partial<SimpleMessage>) => {
          set((state) => ({
            messages: state.messages.map(msg =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          }));
        },

        clearMessages: () => {
          set({ messages: [], unreadCount: 0 });
        },

        setTyping: (isTyping, agentId) => {
          set({
            isTyping,
            currentAgent: isTyping ? agentId || null : null
          });
        },

        setViewMode: (viewMode) => {
          set((state) => ({
            viewMode,
            // Clear unread count when opening chat (not minimized)
            unreadCount: viewMode === VIEW_MODE.MINIMIZED ? state.unreadCount : 0,
          }));
        },

        markAsRead: () => {
          set({ unreadCount: 0 });
        },

        setThreadId: (threadId) => {
          console.log('Setting threadId:', threadId);
          set({ currentThreadId: threadId });
        },

        clearConversation: () => {
          console.log('Clearing conversation and threadId');
          set({
            messages: [],
            unreadCount: 0,
            currentThreadId: null,
            isTyping: false,
            currentAgent: null
          });
        },

        toggleChatboxExpanded: () => {
          set((state) => ({
            isChatboxExpanded: !state.isChatboxExpanded
          }));
        },

        setChatboxExpanded: (expanded) => {
          set({ isChatboxExpanded: expanded });
        },

        setStreamingEnabled: (enabled) => {
          set({ streamingEnabled: enabled });
        },

        setCurrentStreamingMessageId: (id) => {
          set({ currentStreamingMessageId: id });
        },

        setCurrentAgent: (agentId) => {
          set({ currentAgent: agentId });
        },
      }),
    {
      name: 'eneco-chat-widget-store',
    }
  )
);

// Simplified selectors
export const useChatMessages = () => useChatStore((state) => state.messages);
export const useChatTyping = () => useChatStore((state) => state.isTyping);
export const useChatViewMode = () => useChatStore((state) => state.viewMode);
export const useChatUnreadCount = () => useChatStore((state) => state.unreadCount);
export const useChatThreadId = () => useChatStore((state) => state.currentThreadId);
