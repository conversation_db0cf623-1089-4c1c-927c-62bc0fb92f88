/**
 * Widget JSX Component - Separate file for JSX rendering
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { ChatWidget } from './components/ChatWidget';
import type { WidgetConfig } from './types/widget';

export const renderWidget = (
  container: HTMLElement,
  config: WidgetConfig,
  onEvent: (event: any, data: any) => void
) => {
  const root = createRoot(container);
  root.render(<ChatWidget config={config} onEvent={onEvent} />);
  return root;
};
