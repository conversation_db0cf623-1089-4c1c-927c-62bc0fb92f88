import { useCallback, useRef } from 'react';
import { useChatStore } from '../store/chatStore';
import { streamingService } from '../services/streamingService';
import type { StreamingChunk, StreamingState } from '../types/api';

interface UseStreamingOptions {
  onStreamStart?: () => void;
  onStreamEnd?: () => void;
  onStreamError?: (error: Error) => void;
  onChunk?: (chunk: StreamingChunk) => void;
}

export const useStreaming = (options: UseStreamingOptions = {}) => {
  const {
    addMessage,
    setTyping,
    setThreadId,
    setCurrentAgent,
  } = useChatStore();

  const streamingStateRef = useRef<StreamingState>({
    streamedText: '',
    isReceivingImage: false,
    imageBuffer: '',
    pendingImages: [],
    hasReceivedFirstContent: false,
    currentTool: '',
    responseStarted: false,
    hasReceivedFirstToolCall: false,
  });

  const currentStreamingMessageIdRef = useRef<string | null>(null);

  const resetStreamingState = useCallback(() => {
    streamingStateRef.current = {
      streamedText: '',
      isReceivingImage: false,
      imageBuffer: '',
      pendingImages: [],
      hasReceivedFirstContent: false,
      currentTool: '',
      responseStarted: false,
      hasReceivedFirstToolCall: false,
    };
    currentStreamingMessageIdRef.current = null;
  }, []);

  const updateStreamingMessage = useCallback((messageId: string, content: string, images?: string[], toolInfo?: string, isProcessingImage?: boolean) => {
    const { messages: currentMessages } = useChatStore.getState();
    const messageIndex = currentMessages.findIndex(msg => msg.id === messageId);

    if (messageIndex !== -1) {
      const updatedMessages = [...currentMessages];
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        content,
        ...(images && { images }),
        ...(toolInfo && { toolInfo }),
        isStreaming: true,
        isComplete: false,
        isProcessingImage: isProcessingImage ?? false,
      };

      // Update the store directly
      useChatStore.setState({ messages: updatedMessages });
    }
  }, []);

  const completeStreamingMessage = useCallback((messageId: string) => {
    const { messages: currentMessages } = useChatStore.getState();
    const messageIndex = currentMessages.findIndex(msg => msg.id === messageId);
    
    if (messageIndex !== -1) {
      const updatedMessages = [...currentMessages];
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        isStreaming: false,
        isComplete: true,
      };
      
      useChatStore.setState({ messages: updatedMessages });
    }
  }, []);

  const handleChunk = useCallback((chunk: StreamingChunk) => {
    const state = streamingStateRef.current;
    
    options.onChunk?.(chunk);

    switch (chunk.type) {
      case 'thread_id':
        setThreadId(chunk.content);
        break;

      case 'tool_call':
        state.currentTool = chunk.content;
        state.hasReceivedFirstToolCall = true;

        // Set current agent and update typing indicator
        setCurrentAgent(chunk.content);
        setTyping(true, chunk.content);

        // Update the current streaming message with tool info
        if (currentStreamingMessageIdRef.current) {
          updateStreamingMessage(
            currentStreamingMessageIdRef.current,
            state.streamedText,
            state.pendingImages.length > 0 ? state.pendingImages : undefined,
            `${chunk.content} is working...`
          );
        }
        break;

      case 'response_start':
        state.responseStarted = true;
        
        // Create initial agent message if we don't have one yet
        if (!currentStreamingMessageIdRef.current) {
          const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
          currentStreamingMessageIdRef.current = messageId;
          addMessage('', 'agent');
          
          // Update the message ID to match our generated one
          const { messages } = useChatStore.getState();
          const lastMessage = messages[messages.length - 1];
          if (lastMessage) {
            const updatedMessages = [...messages];
            updatedMessages[updatedMessages.length - 1] = {
              ...lastMessage,
              id: messageId,
              isStreaming: true,
              isComplete: false,
            };
            useChatStore.setState({ messages: updatedMessages });
          }
        }
        break;

      case 'text':
        state.streamedText += chunk.content;
        state.hasReceivedFirstContent = true;

        // Update the streaming message
        if (currentStreamingMessageIdRef.current) {
          updateStreamingMessage(
            currentStreamingMessageIdRef.current,
            state.streamedText,
            state.pendingImages.length > 0 ? state.pendingImages : undefined,
            state.currentTool ? `${state.currentTool} is working...` : undefined
          );
        }
        break;

      case 'image':
        // Handle image chunks
        if (chunk.content.startsWith('data:image/')) {
          // Complete image received
          state.pendingImages.push(chunk.content);
          state.isReceivingImage = false;
          state.imageBuffer = '';

          // Update the streaming message with the new image
          if (currentStreamingMessageIdRef.current) {
            updateStreamingMessage(
              currentStreamingMessageIdRef.current,
              state.streamedText,
              [...state.pendingImages],
              state.currentTool ? `${state.currentTool} is working...` : undefined,
              false
            );
          }
        } else {
          // Partial image data
          state.isReceivingImage = true;
          state.imageBuffer += chunk.content;

          // Show processing indicator
          if (currentStreamingMessageIdRef.current) {
            updateStreamingMessage(
              currentStreamingMessageIdRef.current,
              state.streamedText,
              state.pendingImages.length > 0 ? state.pendingImages : undefined,
              state.currentTool ? `${state.currentTool} is working...` : undefined,
              true
            );
          }
        }
        break;

      default:
        console.log('Unknown chunk type:', chunk.type, chunk.content);
    }
  }, [addMessage, setTyping, setThreadId, setCurrentAgent, updateStreamingMessage, options]);

  const processStreamingResponse = useCallback(async (response: Response) => {
    try {
      options.onStreamStart?.();
      resetStreamingState();

      // Process the streaming response
      for await (const chunk of streamingService.processStreamingResponse(response)) {
        handleChunk(chunk);
      }

      // Complete the streaming message
      if (currentStreamingMessageIdRef.current) {
        completeStreamingMessage(currentStreamingMessageIdRef.current);
      }

      // Clear typing indicator and agent
      setTyping(false);
      setCurrentAgent(null);

      options.onStreamEnd?.();
    } catch (error) {
      console.error('Streaming error:', error);
      
      // Clear typing indicator and agent on error
      setTyping(false);
      setCurrentAgent(null);

      // Complete any partial message
      if (currentStreamingMessageIdRef.current) {
        completeStreamingMessage(currentStreamingMessageIdRef.current);
      }

      options.onStreamError?.(error as Error);
      
      // Add error message if no content was received
      if (!streamingStateRef.current.hasReceivedFirstContent) {
        addMessage('Sorry, I encountered an error while processing your request. Please try again.', 'agent');
      }
    } finally {
      resetStreamingState();
    }
  }, [handleChunk, completeStreamingMessage, setTyping, setCurrentAgent, addMessage, resetStreamingState, options]);

  return {
    processStreamingResponse,
    resetStreamingState,
  };
};
