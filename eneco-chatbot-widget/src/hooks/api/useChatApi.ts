import { useMutation } from '@tanstack/react-query';
import { type SendMessagePayload, MUTATION_KEYS } from '../../types/api';
import { WidgetConfig } from '../../types/widget';
import { createChatService } from '../../services/chatService';
import { useChatStore } from '../../store/chatStore';
import { useStreaming } from '../useStreaming';

// Simplified hook for sending messages - adapted for widget with config
export const useSendMessage = (config: WidgetConfig) => {
  const { addMessage, setTyping, setThreadId, currentThreadId, streamingEnabled } = useChatStore();
  const { processStreamingResponse } = useStreaming();

  // Create chat service with config API endpoint
  const chatService = createChatService(config.apiEndpoint);

  return useMutation({
    mutationKey: [MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: async (payload: Omit<SendMessagePayload, 'customerId' | 'accountId'>) => {
      // Add user message immediately
      addMessage(payload.message, 'user');
      // Show typing indicator
      setTyping(true);

      // Create full payload with config values
      const requestPayload: SendMessagePayload = {
        ...payload,
        customerId: config.customerId,
        accountId: config.accountId || '2', // Default account ID
        ...(currentThreadId && { threadId: currentThreadId }),
        stream: streamingEnabled, // Add streaming flag
      };

      // Handle streaming vs non-streaming
      if (streamingEnabled && chatService.sendStreamingMessage) {
        try {
          const streamResponse = await chatService.sendStreamingMessage(requestPayload);
          await processStreamingResponse(streamResponse);
          return null; // Streaming handles response processing
        } catch (streamError) {
          console.warn('Streaming failed, falling back to non-streaming mode:', streamError);
          // Fall back to non-streaming mode
          const response = await chatService.sendMessage({
            ...requestPayload,
            stream: false,
          });
          return response;
        }
      } else {
        // Call regular API
        const response = await chatService.sendMessage(requestPayload);
        return response;
      }
    },
    onSuccess: (data) => {
      // Only process non-streaming responses (streaming is handled by processStreamingResponse)
      if (data !== null) {
        // Hide typing indicator
        setTyping(false);
        // Extract message, threadId, and images from the API response
        let responseText = '';
        let threadId = null;
        let images: string[] | undefined = undefined;

        if (data && typeof data === 'object') {
          const apiData = data as unknown as Record<string, unknown>;

          // Extract the message field (this is the primary response text)
          responseText = (apiData.message || apiData.response || apiData.text || apiData.content) as string;
          // Extract the threadId for conversation continuity
          threadId = apiData.threadId as string;
          // Extract images if present
          if (apiData.images && Array.isArray(apiData.images) && apiData.images.length > 0) {
            images = apiData.images as string[];
          }
          // If no message found, fallback to JSON string
          if (!responseText) {
            responseText = JSON.stringify(data);
          }
        } else if (typeof data === 'string') {
          responseText = data;
        } else {
          responseText = 'Received response from AI';
        }

        // Update threadId in store if received
        if (threadId) {
          setThreadId(threadId);
        }

        addMessage(responseText, 'agent', images);
      }
    },
    onError: (error) => {
      // Hide typing indicator
      setTyping(false);
      // Add error message
      console.error('API Error:', error);
      addMessage('Sorry, I encountered an error. Please try again.', 'agent');
    },
  });
};

// Simplified utility hook for chat operations
export const useChatOperations = (config: WidgetConfig) => {
  const sendMessage = useSendMessage(config);

  return {
    sendMessage: sendMessage.mutate,
    sendMessageAsync: sendMessage.mutateAsync,
    isSendingMessage: sendMessage.isPending,
  };
};
