// Font Face Declarations - Etelka Custom Fonts
// Eneco Brand Typography

// Note: For the widget, fonts will be embedded as base64 data URLs
// to ensure they work within Shadow DOM and don't require external requests

// Etelka Light (300)
@font-face {
  font-family: 'Etelka';
  src: url('./assets/fonts/etelkaLight.woff2') format('woff2'),
       url('./assets/fonts/etelkaLight.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

// Etelka Medium (500)
@font-face {
  font-family: 'Etelka';
  src: url('./assets/fonts/etelkaMedium.woff2') format('woff2'),
       url('./assets/fonts/etelkaMedium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

// Etelka Bold (700)
@font-face {
  font-family: 'Etelka';
  src: url('./assets/fonts/etelkaBold.woff2') format('woff2'),
       url('./assets/fonts/etelkaBold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

// Etelka Black (900)
@font-face {
  font-family: 'Etelka';
  src: url('./assets/fonts/etelkaBlack.woff2') format('woff2'),
       url('./assets/fonts/etelkaBlack.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
