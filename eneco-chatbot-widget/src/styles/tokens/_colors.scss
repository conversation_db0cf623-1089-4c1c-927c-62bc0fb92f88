// Color Tokens - Design System Aligned with Eneco Brand
// Based on actual design system colors from repomix-output.xml colors.ts

// Brand Colors - Eneco Red/Orange (Primary Brand Identity)
$brand-red: #E5384C;        // brandRed - Primary Eneco brand color
$brand-orange: #EA714F;     // brandOrange - Secondary Eneco brand color
$brand-dark-red: #D21242;   // brandDarkRed - Dark variant
$brand-light-red: #F9C7CC;  // brandLightRed - Light variant

// Eneco Red Variants (for comprehensive brand palette)
$eneco-red-600: #e5384c;    // enecoRed600 - Base Eneco red
$eneco-red-700: #d21242;    // enecoRed700 - Dark Eneco red
$eneco-red-800: #bf0639;    // enecoRed800 - Very dark Eneco red
$eneco-red-900: #821034;    // enecoRed900 - Darkest Eneco red

// Orange Variants (complementary to brand red)
$orange-100: #ffe7dc;       // orange100 - Light orange
$orange-300: #ffba8f;       // orange300 - Medium light orange
$orange-400: #ff9363;       // orange400 - Medium orange
$orange-500: #ea714f;       // orange500 - Base orange (brand orange)

// Neutral Colors (Design System neutralColors - exact matches)
$neutral-white: #FFF;       // neutralWhite - Pure white
$neutral-50: #FCFAFA;       // neutral50 - Almost white
$neutral-100: #F8F6F6;      // neutral100 - Very light gray (backgroundSecondary)
$neutral-200: #F0EEEE;      // neutral200 - Light gray (interpolated between 100 and 300)
$neutral-300: #F3F0F0;      // neutral300 - Light gray (backgroundTertiary)
$neutral-400: #DFDCDC;      // neutral400 - Medium light gray (borders)
$neutral-500: #A8A3A3;     // neutral500 - Medium gray (interpolated between 400 and 800)
$neutral-600: #8E8888;     // neutral600 - Medium dark gray (interpolated)
$neutral-700: #7A7474;     // neutral700 - Dark gray (interpolated)
$neutral-800: #716A6A;     // neutral800 - Medium dark gray (low emphasis text)
$neutral-900: #2F2D2D;     // neutral900 - Very dark gray (textPrimary)
$neutral-black: #000;      // neutralBlack - Pure black

// Opacity Colors (Design System opacityColors)
$white-opacity-15: #{$neutral-white}15;  // whiteOpacity15
$white-opacity-30: #{$neutral-white}30;  // whiteOpacity30
$black-opacity-40: #00000040;            // blackOpacity40
$black-opacity-70: #{$neutral-900}70;    // blackOpacity70

// Legacy gray aliases for backward compatibility
$gray-50: $neutral-50;
$gray-100: $neutral-100;
$gray-200: $neutral-200;
$gray-300: $neutral-300;
$gray-400: $neutral-400;
$gray-500: $neutral-500;
$gray-600: $neutral-600;
$gray-700: $neutral-700;
$gray-800: $neutral-800;
$gray-900: $neutral-900;

// Green Colors (Design System secondaryColors.green* and accentGreen*)
$green-50: #F2F7EC;         // green50 - Lightest green
$green-100: #E4EFD8;        // green100 - Very light green
$green-300: #CDE3BB;        // green300 - Light green
$green-500: #7EC389;        // green500 - Medium green
$green-700: #009b65;        // green700 - Dark green (primary functional)
$green-800: #2C6F49;        // green800 - Very dark green
$green-900: #0A4033;        // green900 - Darkest green

// Accent Green Colors (Key functional colors)
$accent-green-100: #e3faea; // accentGreen100 - Light accent green
$accent-green-200: #c0eaca; // accentGreen200 - Medium light accent green
$accent-green-300: #84dc99; // accentGreen300 - Medium accent green
$accent-green-600: #009b65; // accentGreen600 - Primary accent green
$accent-green-700: #007250; // accentGreen700 - Dark accent green (textBrand)
$accent-green-800: #00593f; // accentGreen800 - Very dark accent green

// Success colors (mapped to green variants for semantic use)
$success-50: $green-50;     // Lightest success
$success-100: $green-100;   // Very light success
$success-300: $green-300;   // Light success
$success-500: $green-500;   // Base success
$success-600: $accent-green-600; // Medium dark success (primary functional)
$success-700: $accent-green-700; // Dark success (text brand)
$success-800: $green-800;   // Very dark success
$success-900: $green-900;   // Darkest success

// Error colors (Design System brand colors - using Eneco red for errors)
$error-light: $brand-light-red;  // #F9C7CC - Light error background
$error-base: $brand-red;         // #E5384C - Base error (brandRed)
$error-dark: $brand-dark-red;    // #D21242 - Dark error (brandDarkRed)
$error-darker: $eneco-red-800;   // #bf0639 - Very dark error
$error-darkest: $eneco-red-900;  // #821034 - Darkest error

// Legacy error color mappings for backward compatibility
$error-50: $brand-light-red;     // Light error background
$error-100: $brand-light-red;    // Very light error
$error-500: $error-base;         // Base error
$error-600: $error-dark;         // Medium dark error
$error-700: $error-darker;       // Dark error
$error-800: $error-darker;       // Very dark error
$error-900: $error-darkest;      // Darkest error

// Warning colors (Design System orange variants)
$warning-100: $orange-100;       // #ffe7dc - Light warning background
$warning-300: $orange-300;       // #ffba8f - Medium light warning
$warning-400: $orange-400;       // #ff9363 - Medium warning
$warning-500: $orange-500;       // #ea714f - Base warning (brand orange)

// Info colors (using neutral blues - interpolated for design system)
$info-100: #e6f3ff;        // Light info background
$info-300: #b3d9ff;        // Medium light info
$info-500: #4da6ff;        // Base info
$info-700: #0066cc;        // Dark info
$info-900: #003d7a;        // Darkest info

// Border Colors (Design System borderColors)
$border-divider-low: $neutral-300;      // borderDividerLow (neutral300)
$border-focus: $neutral-900;            // borderFocus (neutral900)
$border-selected: $accent-green-600;    // borderSelected (accentGreen600)

// Outline Colors (Design System outlineColors)
$outline-hover: $neutral-300;           // outlineHover (neutral300)

// Background Colors (backgroundColors from design system)
$background-primary: $neutral-white;        // backgroundPrimary: neutralWhite
$background-secondary: $neutral-100;        // backgroundSecondary: neutral100
$background-tertiary: $neutral-300;         // backgroundTertiary: neutral300
$background-scrim: $black-opacity-70;       // backgroundScrim: blackOpacity70
$background-dark: $neutral-900;             // backgroundDark: neutral900
$background-pressed: $neutral-100;          // backgroundPressed: neutral100

// Text Colors (textColors from design system)
$text-primary: $neutral-900;                // textPrimary: neutral900
$text-inverted: $neutral-white;             // textInverted: neutralWhite
$text-brand: $accent-green-700;             // textBrand: accentGreen700
$text-low-emphasis: $neutral-800;           // textLowEmphasis: neutral800

// Brand Colors (using design system brand colors)
$brand-primary: $brand-red;                 // Primary brand color (Eneco red)
$brand-secondary: $brand-orange;            // Secondary brand color (Eneco orange)
$brand-functional: $accent-green-700;       // Functional brand color (accent green)

// Link Colors (linkColors from design system)
$link-default: $accent-green-700;           // linkDefault: accentGreen700
$link-hover: $accent-green-800;             // linkHover: accentGreen800
$link-visited: $accent-green-600;           // linkVisited: accentGreen600

// Feedback Colors (feedbackColors from design system)
$feedback-success: $accent-green-600;       // feedbackSuccess: accentGreen600
$feedback-warning: $orange-500;             // feedbackWarning: orange500
$feedback-error: $brand-red;                // feedbackError: brandRed
$feedback-info: $info-500;                  // feedbackInfo: interpolated blue

// Eneco Brand Colors (Legacy - mapped to actual design system brand colors)
$eneco-red-primary: $brand-red;             // #E5384C - Primary Eneco brand red
$eneco-red-secondary: $brand-orange;        // #EA714F - Secondary Eneco brand orange
$eneco-red-light: $brand-light-red;         // #F9C7CC - Light Eneco red
$eneco-green: $accent-green-700;            // #007250 - Eneco functional green (textBrand)
$eneco-orange: $brand-orange;               // #EA714F - Eneco orange
$eneco-white: $neutral-white;               // #FFF - White
$eneco-light-gray: $neutral-50;             // #FCFAFA - Light gray from design system

// Additional semantic colors for comprehensive coverage
$primary: $brand-primary;
$secondary: $brand-secondary;
$accent: $brand-functional;
$muted: $neutral-500;

// Surface colors for layering
$surface-primary: $background-primary;
$surface-secondary: $background-secondary;
$surface-tertiary: $background-tertiary;

// Shadow colors
$shadow-light: rgba($neutral-black, 0.1);
$shadow-medium: rgba($neutral-black, 0.15);
$shadow-dark: rgba($neutral-black, 0.25);

// CSS Custom Properties - Design System Aligned
:root {
  // Light theme (default) - Design System Colors

  // Background Colors (backgroundColors from design system)
  --color-background: #{$eneco-light-gray};           // Main app background
  --color-background-primary: #{$background-primary}; // backgroundPrimary (neutralWhite)
  --color-background-secondary: #{$background-secondary}; // backgroundSecondary (neutral100)
  --color-background-tertiary: #{$background-tertiary}; // backgroundTertiary (neutral300)
  --color-background-scrim: #{$background-scrim};     // backgroundScrim (blackOpacity70)
  --color-background-dark: #{$background-dark};       // backgroundDark (neutral900)
  --color-background-pressed: #{$background-pressed}; // backgroundPressed (neutral100)

  // Surface Colors (legacy compatibility)
  --color-surface: #{$background-primary};            // Primary surface (white)
  --color-surface-secondary: #{$background-secondary}; // Secondary surface

  // Border Colors (borderColors from design system)
  --color-border: #{$border-divider-low};             // Default borders (neutral300)
  --color-border-hover: #{$outline-hover};            // Hover state borders (neutral300)
  --color-border-focus: #{$border-focus};             // Focus state borders (neutral900)
  --color-border-selected: #{$border-selected};       // Selected state borders (green500)

  // Text Colors (textColors from design system)
  --color-text-primary: #{$text-primary};             // textPrimary (neutral900)
  --color-text-secondary: #{$text-low-emphasis};      // textLowEmphasis (neutral800)
  --color-text-muted: #{$neutral-400};                // Medium emphasis text
  --color-text-brand: #{$text-brand};                 // textBrand (accentGreen700)
  --color-text-inverted: #{$text-inverted};           // textInverted (neutralWhite)
  --color-text-on-primary: #{$text-inverted};         // Text on primary background (white)

  // Brand Colors (using design system brand colors)
  --color-primary: #{$brand-primary};                 // Primary brand color (brandRed)
  --color-primary-hover: #{$brand-dark-red};          // Primary hover state (brandDarkRed)
  --color-primary-light: #{$brand-light-red};         // Light primary variant (brandLightRed)
  --color-secondary: #{$brand-secondary};             // Secondary brand color (brandOrange)
  --color-brand-functional: #{$brand-functional};     // Functional brand color (accentGreen700)

  // Feedback Colors (feedbackColors from design system)
  --color-success: #{$feedback-success};              // feedbackSuccess (green700)
  --color-error: #{$feedback-error};                  // feedbackError (brandDarkRed)
  --color-warning: #{$feedback-warning};              // feedbackWarning (orange500)
  --color-info: #{$feedback-info};                    // feedbackInfo (accentGreen600)

  // Eneco brand colors (design system aligned)
  --color-eneco-red: #{$eneco-red-primary};           // Primary Eneco red (brandRed)
  --color-eneco-orange: #{$eneco-orange};             // Eneco orange (brandOrange)
  --color-eneco-green: #{$eneco-green};               // Eneco functional green (accentGreen700)
  --color-eneco-white: #{$eneco-white};               // Eneco white (neutralWhite)

  // Accent green variants for hover states
  --color-accent-green-800: #{$accent-green-800};     // Very dark accent green for hover
}

// Dark theme - Design System Aligned
[data-theme="dark"] {
  // Background Colors (dark theme variants using design system neutrals)
  --color-background: #{$neutral-900};                // Main app background (neutral900)
  --color-background-primary: #{$neutral-900};        // backgroundPrimary (dark - neutral900)
  --color-background-secondary: #{$neutral-800};      // backgroundSecondary (dark - neutral800)
  --color-background-tertiary: #{$neutral-400};       // backgroundTertiary (dark - neutral400)
  --color-background-scrim: #{$black-opacity-70};     // backgroundScrim (same as light)
  --color-background-dark: #{$neutral-black};         // backgroundDark (neutralBlack)
  --color-background-pressed: #{$neutral-800};        // backgroundPressed (dark - neutral800)

  // Surface Colors (dark theme)
  --color-surface: #{$neutral-900};                   // Primary surface (dark)
  --color-surface-secondary: #{$neutral-800};         // Secondary surface (dark)

  // Border Colors (dark theme)
  --color-border: #{$neutral-400};                    // Default borders (lighter in dark)
  --color-border-hover: #{$neutral-300};              // Hover state borders
  --color-border-focus: #{$neutral-white};            // Focus state borders (white in dark)
  --color-border-selected: #{$accent-green-300};      // Selected state borders (lighter green)

  // Text Colors (dark theme)
  --color-text-primary: #{$neutral-white};            // textPrimary (white in dark)
  --color-text-secondary: #{$neutral-300};            // textSecondary (light gray in dark)
  --color-text-muted: #{$neutral-400};                // Medium emphasis text
  --color-text-brand: #{$accent-green-300};           // textBrand (lighter green for dark)
  --color-text-inverted: #{$neutral-900};             // textInverted (dark in dark theme)
  --color-text-on-primary: #{$neutral-white};         // Text on primary background

  // Brand Colors (dark theme - keep brand colors consistent)
  --color-primary: #{$brand-red};                     // Primary brand color (same brandRed)
  --color-primary-hover: #{$brand-dark-red};          // Primary hover state (brandDarkRed)
  --color-primary-light: #{$brand-light-red};         // Light primary variant (brandLightRed)
  --color-secondary: #{$brand-orange};                // Secondary brand color (brandOrange)
  --color-brand-functional: #{$accent-green-300};     // Functional brand color (lighter green for dark)

  // Eneco brand colors (dark theme)
  --color-eneco-red: #{$eneco-red-primary};           // Primary Eneco red (same)
  --color-eneco-orange: #{$eneco-orange};             // Eneco orange (same)
  --color-eneco-green: #{$accent-green-300};          // Eneco functional green (lighter for dark)
  --color-eneco-white: #{$eneco-white};               // Eneco white (same)
}
