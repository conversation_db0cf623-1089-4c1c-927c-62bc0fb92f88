// Mixins file - contains all reusable mixins

// Animation utilities
@mixin transition($properties: all, $duration: 0.2s, $timing: ease) {
  transition: $properties $duration $timing;
}

// Responsive breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin mobile-up {
  @media (min-width: #{$breakpoint-sm}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

// Spacing mixins
@mixin padding($size) {
  padding: var(--spacing-#{$size});
}

@mixin padding-x($size) {
  padding-left: var(--spacing-#{$size});
  padding-right: var(--spacing-#{$size});
}

@mixin padding-y($size) {
  padding-top: var(--spacing-#{$size});
  padding-bottom: var(--spacing-#{$size});
}

@mixin margin($size) {
  margin: var(--spacing-#{$size});
}

@mixin margin-x($size) {
  margin-left: var(--spacing-#{$size});
  margin-right: var(--spacing-#{$size});
}

@mixin margin-y($size) {
  margin-top: var(--spacing-#{$size});
  margin-bottom: var(--spacing-#{$size});
}

// Border radius mixins
@mixin rounded($size: base) {
  border-radius: var(--radius-#{$size});
}

@mixin rounded-t($size: base) {
  border-top-left-radius: var(--radius-#{$size});
  border-top-right-radius: var(--radius-#{$size});
}

@mixin rounded-b($size: base) {
  border-bottom-left-radius: var(--radius-#{$size});
  border-bottom-right-radius: var(--radius-#{$size});
}

@mixin rounded-l($size: base) {
  border-top-left-radius: var(--radius-#{$size});
  border-bottom-left-radius: var(--radius-#{$size});
}

@mixin rounded-r($size: base) {
  border-top-right-radius: var(--radius-#{$size});
  border-bottom-right-radius: var(--radius-#{$size});
}

// Shadow mixins
@mixin shadow($size: base) {
  box-shadow: var(--shadow-#{$size});
}

// Typography mixins
@mixin text-xs {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
}

@mixin text-sm {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

@mixin text-base {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
}

@mixin text-lg {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
}

@mixin text-xl {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-tight);
}

@mixin text-2xl {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-tight);
}

@mixin text-3xl {
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-tight);
}

@mixin text-4xl {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
}

// Heading mixins
@mixin heading-1 {
  @include text-4xl;
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-heading);
}

@mixin heading-2 {
  @include text-3xl;
  font-weight: var(--font-weight-bold);
  font-family: var(--font-family-heading);
}

@mixin heading-3 {
  @include text-2xl;
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-family-heading);
}

@mixin heading-4 {
  @include text-xl;
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-family-heading);
}

@mixin heading-5 {
  @include text-lg;
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-heading);
}

@mixin heading-6 {
  @include text-base;
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-heading);
}

// Body text mixins
@mixin body-large {
  @include text-lg;
  font-weight: var(--font-weight-normal);
}

@mixin body-base {
  @include text-base;
  font-weight: var(--font-weight-normal);
}

@mixin body-small {
  @include text-sm;
  font-weight: var(--font-weight-normal);
}

@mixin caption {
  @include text-xs;
  font-weight: var(--font-weight-normal);
  color: var(--color-text-secondary);
}

// Chat specific typography
@mixin chat-message {
  @include text-base;
  line-height: var(--line-height-relaxed);
}

@mixin chat-timestamp {
  @include text-xs;
  color: var(--color-text-muted);
  font-weight: var(--font-weight-normal);
}

// Animation mixins
@mixin fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@mixin slide-up {
  animation: slideUp 0.3s ease-out;
}

@mixin scale-in {
  animation: scaleIn 0.2s ease-out;
}

// Utility mixins
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// Focus styles
@mixin focus-ring {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}

@mixin focus-visible {
  &:focus-visible {
    @include focus-ring;
  }
}
