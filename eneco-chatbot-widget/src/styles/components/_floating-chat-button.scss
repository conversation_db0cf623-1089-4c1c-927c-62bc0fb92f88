// Floating <PERSON><PERSON>
.floating-chat-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: var(--color-primary);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-fixed);
  @include transition(all, 0.3s, ease);
  pointer-events: auto;
  
  &:hover {
    background: var(--color-primary-hover);
    transform: scale(1.05);
    box-shadow: var(--shadow-xl);
  }
  
  &:focus-visible {
    @include focus-ring;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  @include mobile {
    bottom: 16px;
    right: 16px;
    width: 56px;
    height: 56px;
  }
}

.floating-chat-button__avatar {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.floating-chat-button__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.floating-chat-button__badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 20px;
  height: 20px;
  background: var(--color-error);
  color: var(--color-text-inverted);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  @include text-xs;
  font-weight: var(--font-weight-bold);
  padding: 0 6px;
  border: 2px solid var(--color-background-primary);
  box-shadow: var(--shadow-sm);
  
  @include mobile {
    min-width: 18px;
    height: 18px;
    top: -3px;
    right: -3px;
    font-size: 10px;
    padding: 0 4px;
  }
}

.floating-chat-button__pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  background: var(--color-primary);
  opacity: 0;
  pointer-events: none;
  
  &--active {
    animation: pulse 2s infinite;
  }
}

// Pulse animation for new messages
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .floating-chat-button {
    background: var(--color-primary);
    
    &:hover {
      background: var(--color-primary-hover);
    }
  }
  
  .floating-chat-button__badge {
    border-color: var(--color-background-primary);
  }
}
