// Message Input Styles
.message-input {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &--error {
    .message-input__field {
      border-color: var(--color-error);
      
      &:focus {
        border-color: var(--color-error);
        box-shadow: 0 0 0 2px rgba(229, 56, 76, 0.2);
      }
    }
  }
}

.message-input__container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.message-input__field-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-2);
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  @include transition(border-color, 0.2s, ease);
  
  &:focus-within {
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 2px rgba(47, 45, 45, 0.1);
  }
  
  .message-input--expanded & {
    align-items: flex-start;
    padding-top: var(--spacing-3);
  }
}

.message-input__field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
  @include text-base;
  line-height: var(--line-height-normal);
  resize: none;
  min-height: 24px;
  max-height: 120px;
  overflow-y: hidden;
  
  &::placeholder {
    color: var(--color-text-muted);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.message-input__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.message-input__send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: var(--color-border);
  color: var(--color-text-muted);
  border-radius: var(--radius-md);
  cursor: pointer;
  @include transition(all, 0.2s, ease);
  
  &:hover {
    background: var(--color-background-tertiary);
    color: var(--color-text-secondary);
  }
  
  &:focus-visible {
    @include focus-ring;
  }
  
  &--active {
    background: var(--color-primary);
    color: var(--color-text-inverted);
    
    &:hover {
      background: var(--color-primary-hover);
    }
    
    &:disabled {
      background: var(--color-border);
      color: var(--color-text-muted);
      cursor: not-allowed;
      
      &:hover {
        background: var(--color-border);
        color: var(--color-text-muted);
      }
    }
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:hover {
      background: var(--color-border);
      color: var(--color-text-muted);
    }
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

.message-input__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-2);
  min-height: 20px;
}

.message-input__character-count {
  @include text-xs;
  color: var(--color-text-muted);
  
  &--warning {
    color: var(--color-warning);
  }
  
  &--error {
    color: var(--color-error);
    font-weight: var(--font-weight-medium);
  }
}

.message-input__hint {
  @include text-xs;
  color: var(--color-text-muted);
  flex: 1;
}

// Responsive adjustments
@include mobile {
  .message-input__field-wrapper {
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .message-input__send {
    width: 32px;
    height: 32px;
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
  
  .message-input__field {
    @include text-sm;
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .message-input__field-wrapper {
    background: var(--color-background-primary);
    border-color: var(--color-border);
    
    &:focus-within {
      border-color: var(--color-border-focus);
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
    }
  }
  
  .message-input__field {
    color: var(--color-text-primary);
    
    &::placeholder {
      color: var(--color-text-muted);
    }
  }
  
  .message-input__send {
    background: var(--color-border);
    color: var(--color-text-muted);
    
    &:hover {
      background: var(--color-background-tertiary);
      color: var(--color-text-secondary);
    }
    
    &--active {
      background: var(--color-primary);
      color: var(--color-text-inverted);
      
      &:hover {
        background: var(--color-primary-hover);
      }
    }
  }
}
