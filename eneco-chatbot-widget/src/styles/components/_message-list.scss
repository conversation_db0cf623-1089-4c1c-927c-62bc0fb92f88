// Message List Styles
.message-list {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-list__container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  padding: var(--spacing-4);
  
  @include mobile {
    padding: var(--spacing-3);
  }
}

.message-list__content {
  display: flex;
  flex-direction: column;
  gap: var(--chat-message-spacing);
  min-height: 100%;
}

.message-list__group {
  display: flex;
  flex-direction: column;
}

.message-list__messages {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.message-list__typing {
  margin-top: var(--spacing-2);
}

.message-list__end {
  height: 1px;
  flex-shrink: 0;
}

// Empty state
.message-list--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-8);
}

.message-list__empty-state {
  text-align: center;
  max-width: 300px;
}

.message-list__empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.message-list__empty-title {
  @include heading-4;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.message-list__empty-description {
  @include body-base;
  color: var(--color-text-secondary);
  margin: 0;
}

// Scroll to bottom button
.message-list__scroll-to-bottom {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--color-background-primary);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-text-secondary);
  @include transition(all, 0.2s, ease);
  z-index: 10;
  
  &:hover {
    background: var(--color-background-secondary);
    color: var(--color-text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
  
  &:focus-visible {
    @include focus-ring;
  }
  
  &:active {
    transform: translateY(0);
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
  
  @include mobile {
    bottom: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    
    svg {
      width: 18px;
      height: 18px;
    }
  }
}

// Consecutive message styling
.message-bubble--consecutive {
  margin-top: var(--spacing-1);
  
  .message-bubble__timestamp {
    display: none;
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .message-list__scroll-to-bottom {
    background: var(--color-background-primary);
    border-color: var(--color-border);
    
    &:hover {
      background: var(--color-background-secondary);
    }
  }
}
