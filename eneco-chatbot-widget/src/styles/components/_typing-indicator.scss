// Typing Indicator Styles
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  animation: fadeIn 0.3s ease-in-out;
}

.typing-indicator__content {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-2);
  max-width: 80%;
  
  @include mobile {
    max-width: 90%;
  }
}

.typing-indicator__avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  background: var(--color-background-secondary);
  
  @include mobile {
    width: 28px;
    height: 28px;
  }
}

.typing-indicator__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.typing-indicator__bubble {
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-sm);
  padding: var(--spacing-3);
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-width: 60px;
}

.typing-indicator__dots {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 20px;
}

.typing-indicator__dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--color-text-muted);
  animation: typingDot 1.4s infinite ease-in-out;
  
  &:nth-child(1) {
    animation-delay: 0s;
  }
  
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

.typing-indicator__text {
  @include text-xs;
  color: var(--color-text-secondary);
  font-style: italic;
  margin-top: var(--spacing-1);
}

// Typing dot animation
@keyframes typingDot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .typing-indicator__bubble {
    background: var(--color-background-secondary);
  }
  
  .typing-indicator__dot {
    background: var(--color-text-muted);
  }
  
  .typing-indicator__text {
    color: var(--color-text-secondary);
  }
}
