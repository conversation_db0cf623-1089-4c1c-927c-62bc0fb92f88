// Message Bubble Styles
.message-bubble {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  margin-bottom: var(--spacing-3);
  
  &--own {
    align-self: flex-end;
    align-items: flex-end;
  }
  
  &--other {
    align-self: flex-start;
    align-items: flex-start;
  }
  
  @include mobile {
    max-width: 90%;
  }
}

.message-bubble__content {
  background: var(--color-background-secondary);
  border-radius: var(--radius-lg);
  padding: var(--chat-bubble-padding);
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
  
  .message-bubble--own & {
    background: var(--color-primary);
    color: var(--color-text-inverted);
    border-bottom-right-radius: var(--radius-sm);
  }
  
  .message-bubble--other & {
    background: var(--color-background-secondary);
    color: var(--color-text-primary);
    border-bottom-left-radius: var(--radius-sm);
  }
}

.message-bubble__tool-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  @include text-sm;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-2);
  padding: var(--spacing-2);
  background: var(--color-background-tertiary);
  border-radius: var(--radius-md);
  
  .message-bubble--own & {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
  }
}

.message-bubble__tool-icon {
  font-size: 14px;
  animation: pulse 1.5s infinite;
}

.message-bubble__text {
  @include chat-message;
  margin: 0;
  
  &--streaming {
    position: relative;
  }
}

.message-bubble__streaming-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background: currentColor;
  margin-left: 2px;
  animation: blink 1s infinite;
  vertical-align: text-bottom;
}

.message-bubble__processing {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  @include text-sm;
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  padding: var(--spacing-2);
  background: var(--color-background-tertiary);
  border-radius: var(--radius-md);
  
  .message-bubble--own & {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
  }
}

.message-bubble__processing-icon {
  font-size: 14px;
  animation: pulse 1.5s infinite;
}

.message-bubble__images {
  margin-top: var(--spacing-3);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.message-bubble__image {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
  cursor: pointer;
  @include transition(transform, 0.2s, ease);
  
  &:hover {
    transform: scale(1.02);
  }
}

.message-bubble__timestamp {
  @include chat-timestamp;
  margin-top: var(--spacing-1);
  
  .message-bubble--own & {
    text-align: right;
  }
  
  .message-bubble--other & {
    text-align: left;
  }
}

// Markdown content styling
.markdown-content {
  line-height: var(--line-height-relaxed);
  
  &--plain {
    white-space: pre-wrap;
  }
  
  &--streaming {
    .markdown-p:last-child {
      margin-bottom: 0;
    }
  }
}

// Markdown elements
.markdown-h1,
.markdown-h2,
.markdown-h3,
.markdown-h4,
.markdown-h5,
.markdown-h6 {
  margin: var(--spacing-3) 0 var(--spacing-2) 0;
  line-height: var(--line-height-tight);
  
  &:first-child {
    margin-top: 0;
  }
}

.markdown-h1 { @include text-xl; font-weight: var(--font-weight-bold); }
.markdown-h2 { @include text-lg; font-weight: var(--font-weight-bold); }
.markdown-h3 { @include text-base; font-weight: var(--font-weight-semibold); }
.markdown-h4 { @include text-sm; font-weight: var(--font-weight-semibold); }
.markdown-h5 { @include text-sm; font-weight: var(--font-weight-medium); }
.markdown-h6 { @include text-xs; font-weight: var(--font-weight-medium); }

.markdown-p {
  margin: 0 0 var(--spacing-3) 0;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.markdown-ul,
.markdown-ol {
  margin: var(--spacing-2) 0;
  padding-left: var(--spacing-5);
}

.markdown-li {
  margin: var(--spacing-1) 0;
}

.markdown-code-inline {
  background: var(--color-background-tertiary);
  padding: 2px 4px;
  border-radius: var(--radius-sm);
  @include text-sm;
  font-family: var(--font-family-mono);
  
  .message-bubble--own & {
    background: rgba(255, 255, 255, 0.2);
  }
}

.markdown-pre {
  background: var(--color-background-tertiary);
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin: var(--spacing-2) 0;
  
  .message-bubble--own & {
    background: rgba(255, 255, 255, 0.1);
  }
}

.markdown-code-block {
  @include text-sm;
  font-family: var(--font-family-mono);
}

.markdown-link {
  color: var(--color-brand-functional);
  text-decoration: underline;
  
  &:hover {
    color: var(--color-accent-green-800);
  }
  
  .message-bubble--own & {
    color: rgba(255, 255, 255, 0.9);
    
    &:hover {
      color: rgba(255, 255, 255, 1);
    }
  }
}

.markdown-strong {
  font-weight: var(--font-weight-bold);
}

.markdown-em {
  font-style: italic;
}

.markdown-blockquote {
  border-left: 3px solid var(--color-border);
  padding-left: var(--spacing-3);
  margin: var(--spacing-2) 0;
  color: var(--color-text-secondary);
  
  .message-bubble--own & {
    border-left-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.8);
  }
}

.markdown-table-wrapper {
  overflow-x: auto;
  margin: var(--spacing-2) 0;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  @include text-sm;
}

.markdown-th,
.markdown-td {
  padding: var(--spacing-2);
  border: 1px solid var(--color-border);
  text-align: left;
}

.markdown-th {
  background: var(--color-background-tertiary);
  font-weight: var(--font-weight-semibold);
}

.markdown-hr {
  border: none;
  height: 1px;
  background: var(--color-border);
  margin: var(--spacing-4) 0;
}

// Animations
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

// Dark theme adjustments
[data-theme="dark"] {
  .message-bubble__content {
    .message-bubble--other & {
      background: var(--color-background-secondary);
      color: var(--color-text-primary);
    }
  }
  
  .markdown-code-inline,
  .markdown-pre {
    background: var(--color-background-tertiary);
  }
  
  .markdown-th {
    background: var(--color-background-tertiary);
  }
}
