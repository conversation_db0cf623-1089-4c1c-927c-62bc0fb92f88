// Chat Container Styles
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: var(--color-background-primary);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-index-modal);
  pointer-events: auto;
  
  // Fullpage mode
  &--fullpage {
    height: 100vh;
    width: 100vw;
    border-radius: 0;
    box-shadow: none;
  }
  
  // Chatbox mode
  &--chatbox {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: var(--floating-chat-size);
    max-height: var(--floating-chat-max-height);
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--color-border);
    
    // Expanded chatbox
    &.chat-container--expanded {
      width: var(--floating-chat-expanded-size);
      max-height: var(--floating-chat-expanded-max-height);
    }
    
    @include mobile {
      bottom: 10px;
      right: 10px;
      left: 10px;
      width: auto;
      max-width: none;
    }
  }
}

// Header
.chat-container__header {
  flex-shrink: 0;
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-4);
  
  .chat-container--chatbox & {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
}

.chat-container__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-3);
}

.chat-container__title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
  min-width: 0;
}

.chat-container__avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  overflow: hidden;
  flex-shrink: 0;
  background: var(--color-background-secondary);
}

.chat-container__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-container__title-text {
  min-width: 0;
  
  h2 {
    @include text-lg;
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
    line-height: 1.2;
  }
}

.chat-container__subtitle {
  @include text-sm;
  color: var(--color-text-secondary);
  display: block;
  margin-top: 2px;
}

.chat-container__header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.chat-container__action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  @include transition(all, 0.2s, ease);
  
  &:hover {
    background: var(--color-background-secondary);
    color: var(--color-text-primary);
  }
  
  &:focus-visible {
    @include focus-ring;
  }
  
  &--active {
    background: var(--color-primary);
    color: var(--color-text-inverted);
    
    &:hover {
      background: var(--color-primary-hover);
    }
  }
  
  svg {
    width: 18px;
    height: 18px;
  }
}

// Content
.chat-container__content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  
  .chat-container--chatbox & {
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
    overflow: hidden;
  }
}

.chat-container__messages {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.chat-container__input {
  flex-shrink: 0;
  background: var(--color-background-primary);
  border-top: 1px solid var(--color-border);
  padding: var(--spacing-4);
  
  .chat-container--chatbox & {
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
}

// Responsive adjustments
@include mobile {
  .chat-container {
    &--fullpage {
      height: 100vh;
      height: 100dvh; // Use dynamic viewport height on mobile
    }
  }
  
  .chat-container__header {
    padding: var(--spacing-3);
  }
  
  .chat-container__input {
    padding: var(--spacing-3);
  }
  
  .chat-container__action {
    width: 32px;
    height: 32px;
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

// Dark theme adjustments
[data-theme="dark"] {
  .chat-container {
    background: var(--color-background-primary);
    border-color: var(--color-border);
  }
  
  .chat-container__header {
    background: var(--color-background-primary);
    border-bottom-color: var(--color-border);
  }
  
  .chat-container__input {
    background: var(--color-background-primary);
    border-top-color: var(--color-border);
  }
}
