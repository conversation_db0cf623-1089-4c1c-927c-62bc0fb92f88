// Main SCSS file for Eneco Chatbot Widget
// This file imports all design tokens and component styles

// Import design tokens first
@import './tokens/fonts';
@import './tokens/colors';
@import './tokens/typography';
@import './tokens/spacing';
@import './tokens/mixins';

// Base styles for the widget
* {
  box-sizing: border-box;
}

// Widget root styles - ensure proper isolation
.eneco-chat-widget {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: transparent;
  
  // Reset any inherited styles
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  // Ensure proper text rendering
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// Import component styles
@import './components/chat-container';
@import './components/message-list';
@import './components/message-input';
@import './components/message-bubble';
@import './components/floating-chat-button';
@import './components/typing-indicator';

// Animation keyframes
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// Utility classes
.sr-only {
  @include visually-hidden;
}

.fade-in {
  @include fade-in;
}

.slide-up {
  @include slide-up;
}

.scale-in {
  @include scale-in;
}

// Focus management for accessibility
.eneco-chat-widget {
  button:focus-visible,
  input:focus-visible,
  textarea:focus-visible {
    @include focus-ring;
  }
}

// Scrollbar styling for webkit browsers
.eneco-chat-widget {
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    background: var(--color-background-secondary);
    border-radius: var(--radius-full);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--color-border);
    border-radius: var(--radius-full);
    
    &:hover {
      background: var(--color-text-muted);
    }
  }
}

// Dark theme adjustments
[data-theme="dark"] .eneco-chat-widget {
  ::-webkit-scrollbar-track {
    background: var(--color-background-tertiary);
  }
  
  ::-webkit-scrollbar-thumb {
    background: var(--color-border);
    
    &:hover {
      background: var(--color-text-secondary);
    }
  }
}
