import React, { useEffect } from 'react';
import { useChatStore } from '../../store/chatStore';
import { useSendMessage } from '../../hooks/api/useChatApi';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { Maximize2, Square, Minimize, Minus, Zap, ZapOff } from 'lucide-react';
import clsx from 'clsx';
import { VIEW_MODE } from '../../types/chat';
import type { WidgetConfig, WidgetEventType } from '../../types/widget';
import edwinImage from '../../assets/edwin.png';

interface ChatContainerWidgetProps {
  config: WidgetConfig;
  onEvent: (event: WidgetEventType, data: any) => void;
  className?: string;
  children?: React.ReactNode;
}

const ChatContainer: React.FC<ChatContainerWidgetProps> = ({
  config,
  onEvent,
  className,
  children,
}) => {
  const {
    messages,
    isTyping,
    viewMode,
    unreadCount,
    currentThreadId,
    isChatboxExpanded,
    streamingEnabled,
    setViewMode,
    markAsRead,
    toggleChatboxExpanded,
    setStreamingEnabled,
  } = useChatStore();

  const sendMessageMutation = useSendMessage(config);
  
  // Mark messages as read when chat is opened
  useEffect(() => {
    if (viewMode !== VIEW_MODE.MINIMIZED && unreadCount > 0) {
      markAsRead();
    }
  }, [viewMode, unreadCount, markAsRead]);

  const handleSendMessage = async (content: string) => {
    try {
      // Send message via API with config values
      await sendMessageMutation.mutateAsync({
        message: content,
      });
      
      // Emit message event
      onEvent('sendMessage', { message: content });
    } catch (error) {
      console.error('Failed to send message:', error);
      onEvent('error', { error: error as Error, context: 'sendMessage' });
    }
  };

  // Handler functions for the 3-state system
  const handleMinimizeToLogo = () => {
    setViewMode(VIEW_MODE.MINIMIZED);
    // Reset expanded state when minimizing
    if (isChatboxExpanded) {
      toggleChatboxExpanded();
    }
  };

  const handleToChatbox = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };



  const handleToggleExpanded = () => {
    toggleChatboxExpanded();
  };

  const handleToggleStreaming = () => {
    setStreamingEnabled(!streamingEnabled);
  };

  // State checks
  const isFullpage = viewMode === VIEW_MODE.FULLPAGE;
  const isChatbox = viewMode === VIEW_MODE.CHATBOX;
  const isMinimized = viewMode === VIEW_MODE.MINIMIZED;

  // Don't render chat container when minimized
  if (isMinimized) {
    return null;
  }

  return (
    <div
      className={clsx(
        'chat-container',
        {
          'chat-container--fullpage': isFullpage,
          'chat-container--chatbox': isChatbox,
          'chat-container--expanded': isChatbox && isChatboxExpanded,
        },
        className
      )}
    >
      {/* Header */}
      <div className="chat-container__header">
        <div className="chat-container__header-content">
          <div className="chat-container__title">
            <div className="chat-container__avatar">
              <img
                src={edwinImage}
                alt="Edwin - Eneco Assistant"
                className="chat-container__avatar-image"
              />
            </div>
            <div className="chat-container__title-text">
              <h2>Edwin</h2>
              <span className="chat-container__subtitle">Eneco Assistant</span>
            </div>
          </div>

          <div className="chat-container__header-actions">
            {/* State 1: Full Page Mode - show streaming toggle, restore down and minimize buttons */}
            {isFullpage && (
              <>
                <button
                  onClick={handleToggleStreaming}
                  className={clsx("chat-container__action", {
                    "chat-container__action--active": streamingEnabled
                  })}
                  aria-label={streamingEnabled ? "Disable streaming" : "Enable streaming"}
                  title={streamingEnabled ? "Disable streaming" : "Enable streaming"}
                >
                  {streamingEnabled ? <Zap size={18} /> : <ZapOff size={18} />}
                </button>
                <button
                  onClick={handleToChatbox}
                  className="chat-container__action"
                  aria-label="Restore to chatbox"
                  title="Restore to chatbox"
                >
                  <Square size={18} />
                </button>
                <button
                  onClick={handleMinimizeToLogo}
                  className="chat-container__action"
                  aria-label="Minimize to logo"
                  title="Minimize to logo"
                >
                  <Minimize size={18} />
                </button>
              </>
            )}

            {/* State 2: Chatbox Mode - show streaming toggle, expand/contract and minimize buttons */}
            {isChatbox && (
              <>
                <button
                  onClick={handleToggleStreaming}
                  className={clsx("chat-container__action", {
                    "chat-container__action--active": streamingEnabled
                  })}
                  aria-label={streamingEnabled ? "Disable streaming" : "Enable streaming"}
                  title={streamingEnabled ? "Disable streaming" : "Enable streaming"}
                >
                  {streamingEnabled ? <Zap size={18} /> : <ZapOff size={18} />}
                </button>
                <button
                  onClick={handleToggleExpanded}
                  className="chat-container__action"
                  aria-label={isChatboxExpanded ? "Contract chatbox" : "Expand chatbox"}
                  title={isChatboxExpanded ? "Contract chatbox" : "Expand chatbox"}
                >
                  {isChatboxExpanded ? <Minimize size={18} /> : <Maximize2 size={18} />}
                </button>
                <button
                  onClick={handleMinimizeToLogo}
                  className="chat-container__action"
                  aria-label="Minimize to logo"
                  title="Minimize to logo"
                >
                  <Minus size={18} />
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="chat-container__content">
        <div className="chat-container__messages">
          <MessageList
            messages={messages}
            isTyping={isTyping}
          />
        </div>

        <div className="chat-container__input">
          {/* Debug info - only show in debug mode */}
          {config.debugMode && currentThreadId && (
            <div style={{
              fontSize: '10px',
              color: '#666',
              padding: '4px 8px',
              background: '#f0f0f0',
              borderRadius: '4px',
              marginBottom: '8px'
            }}>
              Thread ID: {currentThreadId}
            </div>
          )}

          <MessageInput
            onSendMessage={handleSendMessage}
            disabled={sendMessageMutation.isPending}
            placeholder="Ask me anything about Eneco..."
          />
        </div>
      </div>

      {children}
    </div>
  );
};

export default ChatContainer;
