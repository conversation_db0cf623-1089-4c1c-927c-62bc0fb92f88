import React, { useEffect, useRef, useState, useCallback } from 'react';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import { useChatStore } from '../../store/chatStore';
import { SimpleMessage } from '../../types/chat';
import clsx from 'clsx';

interface MessageListProps {
  messages?: SimpleMessage[];
  isTyping?: boolean;
  autoScroll?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MessageList: React.FC<MessageListProps> = ({
  messages: propMessages,
  isTyping: propIsTyping,
  autoScroll = true,
  className,
  children,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const lastStreamingContentRef = useRef<string>('');
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use store values if props not provided
  const storeMessages = useChatStore((state) => state.messages);
  const storeIsTyping = useChatStore((state) => state.isTyping);

  const messages = propMessages || storeMessages;
  const isTyping = propIsTyping !== undefined ? propIsTyping : storeIsTyping;

  // Find currently streaming message
  const streamingMessage = messages.find(msg => msg.isStreaming && !msg.isComplete);
  
  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = (smooth = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end',
      });
    }
  };

  // Enhanced scroll function for streaming content
  const scrollToBottomForStreaming = useCallback(() => {
    // Clear any existing timeout to avoid multiple rapid scrolls
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Use a small delay to batch rapid updates during streaming
    scrollTimeoutRef.current = setTimeout(() => {
      if (messagesEndRef.current && !isUserScrolling) {
        messagesEndRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        });
      }
    }, 50); // Small delay to batch rapid streaming updates
  }, [isUserScrolling]);

  // Check if user is near bottom (within reasonable threshold)
  const isNearBottom = () => {
    if (!containerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    return scrollHeight - scrollTop - clientHeight < 100; // 100px threshold for "near bottom"
  };
  
  // Handle scroll events to detect user scrolling
  const handleScroll = () => {
    if (!containerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
    const nearBottom = isNearBottom();

    setIsUserScrolling(!isAtBottom);
    setShowScrollToBottom(!isAtBottom && messages.length > 0);

    // If user scrolls back near bottom during streaming, resume auto-scroll
    if (nearBottom && streamingMessage) {
      setIsUserScrolling(false);
    }
  };
  
  // Auto-scroll effect for new messages and typing indicator
  useEffect(() => {
    if (autoScroll && !isUserScrolling) {
      scrollToBottom();
    }
  }, [messages.length, isTyping, autoScroll, isUserScrolling]);

  // Enhanced auto-scroll effect for streaming content
  useEffect(() => {
    if (!streamingMessage || !autoScroll) return;

    const currentContent = streamingMessage.content;

    // Check if streaming content has changed
    if (currentContent !== lastStreamingContentRef.current) {
      lastStreamingContentRef.current = currentContent;

      // Only auto-scroll if user hasn't manually scrolled up
      if (!isUserScrolling) {
        scrollToBottomForStreaming();
      }
    }
  }, [streamingMessage?.content, streamingMessage, autoScroll, isUserScrolling, scrollToBottomForStreaming]);

  // Effect to handle typing indicator appearance
  useEffect(() => {
    if (isTyping && !isUserScrolling && autoScroll) {
      // Immediate scroll when typing starts
      scrollToBottom(true);
    }
  }, [isTyping, isUserScrolling, autoScroll]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);
  
  // Reset user scrolling when reaching bottom
  useEffect(() => {
    if (!containerRef.current) return;
    
    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 10;

    if (isAtBottom) {
      setIsUserScrolling(false);
      setShowScrollToBottom(false);
    }
  }, []);

  // Intersection Observer for auto-scroll detection
  useEffect(() => {
    if (!messagesEndRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsUserScrolling(false);
          setShowScrollToBottom(false);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(messagesEndRef.current);

    return () => observer.disconnect();
  }, []);

  const handleScrollToBottom = () => {
    setIsUserScrolling(false);
    scrollToBottom();
  };

  const groupedMessages = React.useMemo(() => {
    const groups: Array<{
      date: string;
      messages: SimpleMessage[];
    }> = [];

    messages.forEach((message) => {
      const messageDate = message.timestamp.toDateString();
      const lastGroup = groups[groups.length - 1];

      if (lastGroup && lastGroup.date === messageDate) {
        lastGroup.messages.push(message);
      } else {
        groups.push({
          date: messageDate,
          messages: [message],
        });
      }
    });

    return groups;
  }, [messages]);


  if (messages.length === 0 && !isTyping) {
    return (
      <div className={clsx('message-list message-list--empty', className)}>
        <div className="message-list__empty-state">
          <div className="message-list__empty-icon">💬</div>
          <h3 className="message-list__empty-title">Start a conversation</h3>
          <p className="message-list__empty-description">
            Send a message to begin chatting with our AI assistant.
          </p>
        </div>
        {children}
      </div>
    );
  }

  return (
    <div className={clsx('message-list', className)}>
      <div
        ref={containerRef}
        className="message-list__container"
        onScroll={handleScroll}
      >
        <div className="message-list__content">
          {groupedMessages.map((group) => (
            <div key={group.date} className="message-list__group">
              <div className="message-list__messages">
                {group.messages.map((message, index) => {
                  const prevMessage = group.messages[index - 1];
                  const isConsecutive =
                    prevMessage &&
                    prevMessage.sender === message.sender &&
                    message.timestamp.getTime() - prevMessage.timestamp.getTime() < 60000; // 1 minute

                  return (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      isOwn={message.sender === 'user'}
                      showTimestamp={!isConsecutive}
                      className={clsx({
                        'message-bubble--consecutive': isConsecutive,
                      })}
                    />
                  );
                })}
              </div>
            </div>
          ))}

          {isTyping && (
            <TypingIndicator className="message-list__typing" />
          )}

          <div ref={messagesEndRef} className="message-list__end" />
        </div>
      </div>

      {showScrollToBottom && (
        <button
          onClick={handleScrollToBottom}
          className="message-list__scroll-to-bottom"
          aria-label="Scroll to bottom"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 14l5 5 5-5z" />
          </svg>
        </button>
      )}

      {children}
    </div>
  );
};

export default MessageList;
