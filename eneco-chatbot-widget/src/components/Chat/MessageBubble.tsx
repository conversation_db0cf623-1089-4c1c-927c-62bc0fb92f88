import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import clsx from 'clsx';
import MarkdownRenderer from './MarkdownRenderer';
import type { SimpleMessage } from '../../types/chat';

interface MessageBubbleProps {
  message: SimpleMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showTimestamp = true,
  className,
  children,
}) => {
  const formatTimestamp = (timestamp: Date) => {
    try {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <div
      className={clsx(
        'message-bubble',
        {
          'message-bubble--own': isOwn,
          'message-bubble--other': !isOwn,
          'message-bubble--has-images': message.images && message.images.length > 0,
        },
        className
      )}
    >
      <div className="message-bubble__content">
        {/* Show tool info for streaming messages */}
        {message.toolInfo && message.isStreaming && (
          <div className="message-bubble__tool-info">
            <span className="message-bubble__tool-icon">⚡</span>
            {message.toolInfo}
          </div>
        )}

        <div className={clsx('message-bubble__text', {
          'message-bubble__text--streaming': message.isStreaming
        })}>
          {/* Render agent messages with markdown support */}
          {message.sender === 'agent' ? (
            <MarkdownRenderer
              content={message.content}
              isStreaming={message.isStreaming ?? false}
            />
          ) : (
            message.content
          )}
          {/* Show streaming cursor */}
          {message.isStreaming && !message.isComplete && (
            <span className="message-bubble__streaming-cursor">|</span>
          )}
        </div>

        {/* Show image processing indicator */}
        {message.isProcessingImage && (
          <div className="message-bubble__processing">
            <span className="message-bubble__processing-icon">🖼️</span>
            Processing image...
          </div>
        )}

        {/* Render images if present - simplified version */}
        {message.images && message.images.length > 0 && (
          <div className="message-bubble__images">
            {message.images.map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`Shared image ${index + 1}`}
                className="message-bubble__image"
                loading="lazy"
              />
            ))}
          </div>
        )}
      </div>
      
      {showTimestamp && (
        <div className="message-bubble__timestamp">
          {formatTimestamp(message.timestamp)}
        </div>
      )}

      {children}
    </div>
  );
};

export default MessageBubble;
