/**
 * ChatWidget - Main widget component that wraps all chat functionality
 * 
 * This component serves as the root of the chat widget and handles:
 * - Widget configuration
 * - Event communication with the host page
 * - Provider setup (QueryClient, etc.)
 * - Theme management
 */

import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WidgetProps, WidgetEventType } from '../types/widget';
import { useChatStore } from '../store/chatStore';
import { VIEW_MODE } from '../types/chat';
import ChatContainer from './Chat/ChatContainer';
import FloatingChatButton from './Chat/FloatingChatButton';

// Create a query client for the widget
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

interface ChatWidgetInternalProps {
  config: WidgetProps['config'];
  onEvent: WidgetProps['onEvent'];
}

const ChatWidgetInternal: React.FC<ChatWidgetInternalProps> = ({ config, onEvent }) => {
  const { setViewMode, viewMode, unreadCount } = useChatStore();

  // Initialize widget with config
  useEffect(() => {
    // Set initial view mode from config
    if (config.initialMode) {
      setViewMode(config.initialMode as any);
    }

    // Apply theme
    if (config.theme) {
      const root = document.documentElement;
      if (config.theme === 'dark') {
        root.setAttribute('data-theme', 'dark');
      } else if (config.theme === 'light') {
        root.setAttribute('data-theme', 'light');
      } else if (config.theme === 'auto') {
        // Detect system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
      }
    }

    // Apply custom colors if provided
    if (config.primaryColor) {
      document.documentElement.style.setProperty('--color-primary', config.primaryColor);
    }
    if (config.secondaryColor) {
      document.documentElement.style.setProperty('--color-secondary', config.secondaryColor);
    }
    if (config.fontFamily) {
      document.documentElement.style.setProperty('--font-family-sans', config.fontFamily);
    }
  }, [config, setViewMode]);

  // Handle view mode changes
  useEffect(() => {
    onEvent('modeChange', { mode: viewMode });
  }, [viewMode, onEvent]);

  const handleRestoreFromMinimized = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };

  // Apply CSS classes to widget container based on view mode
  useEffect(() => {
    const container = document.querySelector('#' + (config.containerId || 'eneco-chat-root'));
    if (container) {
      // Remove all chat-related classes
      container.classList.remove('chat-fullpage', 'chat-chatbox', 'chat-minimized');

      // Add the appropriate class based on current view mode
      switch (viewMode) {
        case VIEW_MODE.FULLPAGE:
          container.classList.add('chat-fullpage');
          break;
        case VIEW_MODE.CHATBOX:
          container.classList.add('chat-chatbox');
          break;
        case VIEW_MODE.MINIMIZED:
          container.classList.add('chat-minimized');
          break;
      }
    }
  }, [viewMode, config.containerId]);

  return (
    <div className="eneco-chat-widget">
      <ChatContainer config={config} onEvent={onEvent} />

      {/* Floating Chat Button - only visible when minimized */}
      <FloatingChatButton
        isVisible={viewMode === VIEW_MODE.MINIMIZED}
        onClick={handleRestoreFromMinimized}
        unreadCount={unreadCount}
      />
    </div>
  );
};

export const ChatWidget: React.FC<WidgetProps> = ({ config, onEvent }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <ChatWidgetInternal config={config} onEvent={onEvent} />
    </QueryClientProvider>
  );
};
