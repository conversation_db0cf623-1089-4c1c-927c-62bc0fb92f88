/**
 * Eneco Chatbot Widget - Main Entry Point
 * 
 * This file serves as the main entry point for the standalone chatbot widget.
 * It creates a global API that allows host pages to initialize and control
 * the chatbot through a simple JavaScript interface.
 */

import { createRoot } from 'react-dom/client';
import { ChatWidget } from './components/ChatWidget';
import { WidgetConfig, WidgetAPI, WidgetEventType } from './types/widget';
import './styles/main.scss';

// Global widget state
let widgetInstance: WidgetAPI | null = null;
let shadowRoot: ShadowRoot | null = null;
let reactRoot: any = null;

// Default configuration
const DEFAULT_CONFIG: Partial<WidgetConfig> = {
  containerId: 'eneco-chat-root',
  theme: 'light',
  position: 'bottom-right',
  initialMode: 'minimized',
  streamingEnabled: true,
  autoOpen: false,
  showUnreadCount: true,
  enableSounds: false,
  primaryColor: '#E5384C',
  fontFamily: 'Etelka, Inter, sans-serif'
};

// Event system
const eventListeners: Map<WidgetEventType, Function[]> = new Map();

/**
 * Initialize the chatbot widget
 */
function init(config: Partial<WidgetConfig>): WidgetAPI {
  if (widgetInstance) {
    console.warn('Eneco Chat Widget is already initialized');
    return widgetInstance;
  }

  // Validate required config
  if (!config.apiEndpoint) {
    throw new Error('apiEndpoint is required');
  }
  if (!config.customerId) {
    throw new Error('customerId is required');
  }

  // Merge with defaults
  const finalConfig: WidgetConfig = {
    ...DEFAULT_CONFIG,
    ...config
  } as WidgetConfig;

  // Create or find container
  let container = document.getElementById(finalConfig.containerId!);
  if (!container) {
    container = document.createElement('div');
    container.id = finalConfig.containerId!;
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 999999;
    `;
    document.body.appendChild(container);
  }

  // Create Shadow DOM for style isolation
  if (!shadowRoot) {
    shadowRoot = container.attachShadow({ mode: 'open' });
    
    // Create shadow container
    const shadowContainer = document.createElement('div');
    shadowContainer.style.cssText = `
      position: relative;
      width: 100%;
      height: 100%;
      pointer-events: none;
    `;
    shadowRoot.appendChild(shadowContainer);

    // Mount React app in shadow DOM
    reactRoot = createRoot(shadowContainer);
    reactRoot.render(<ChatWidget config={finalConfig} onEvent={emitEvent} />);
  }

  // Create widget API
  widgetInstance = {
    show: () => {
      if (container) {
        container.style.display = 'block';
      }
      emitEvent('show', {});
    },

    hide: () => {
      if (container) {
        container.style.display = 'none';
      }
      emitEvent('hide', {});
    },

    setMode: (mode) => {
      // This will be implemented to communicate with the React component
      emitEvent('modeChange', { mode });
    },

    sendMessage: (message) => {
      // This will be implemented to communicate with the React component
      emitEvent('sendMessage', { message });
    },

    clearChat: () => {
      // This will be implemented to communicate with the React component
      emitEvent('clearChat', {});
    },

    updateConfig: (newConfig) => {
      // This will be implemented to update the React component config
      emitEvent('configUpdate', { config: newConfig });
    },

    destroy: () => {
      if (reactRoot) {
        reactRoot.unmount();
        reactRoot = null;
      }
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
      }
      shadowRoot = null;
      widgetInstance = null;
      eventListeners.clear();
      emitEvent('destroy', {});
    },

    on: (event, callback) => {
      if (!eventListeners.has(event)) {
        eventListeners.set(event, []);
      }
      eventListeners.get(event)!.push(callback);
    },

    off: (event, callback) => {
      const listeners = eventListeners.get(event);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    }
  };

  // Emit ready event
  setTimeout(() => {
    emitEvent('ready', { config: finalConfig });
  }, 0);

  return widgetInstance;
}

/**
 * Emit widget events
 */
function emitEvent(event: WidgetEventType, data: any) {
  const listeners = eventListeners.get(event);
  if (listeners) {
    listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event listener:`, error);
      }
    });
  }
}

/**
 * Get current widget instance
 */
function getInstance(): WidgetAPI | null {
  return widgetInstance;
}

// Global API
const EnecoChat = {
  init,
  getInstance,
  version: __WIDGET_VERSION__ || '1.0.0'
};

// Expose global API
declare global {
  interface Window {
    EnecoChat: typeof EnecoChat;
  }
}

window.EnecoChat = EnecoChat;

// Export for module usage
export default EnecoChat;
export { init, getInstance };
export type { WidgetConfig, WidgetAPI } from './types/widget';
