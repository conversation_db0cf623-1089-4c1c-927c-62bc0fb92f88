/**
 * Widget Configuration and API Types
 */

export interface WidgetConfig {
  // Required
  apiEndpoint: string;
  customerId: string;
  
  // Optional
  accountId?: string;
  containerId?: string;
  theme?: 'light' | 'dark' | 'auto';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  initialMode?: 'minimized' | 'chatbox' | 'fullpage';
  streamingEnabled?: boolean;
  
  // Styling
  primaryColor?: string;
  secondaryColor?: string;
  fontFamily?: string;
  borderRadius?: string;
  
  // Behavior
  autoOpen?: boolean;
  showUnreadCount?: boolean;
  enableSounds?: boolean;
  maxMessages?: number;
  
  // Advanced
  debugMode?: boolean;
  customHeaders?: Record<string, string>;
  retryAttempts?: number;
  timeout?: number;
}

export type ViewMode = 'fullpage' | 'chatbox' | 'minimized';

export type WidgetEventType = 
  | 'ready'
  | 'show' 
  | 'hide'
  | 'modeChange'
  | 'message'
  | 'sendMessage'
  | 'clearChat'
  | 'configUpdate'
  | 'destroy'
  | 'error';

export interface WidgetEventData {
  ready: { config: WidgetConfig };
  show: {};
  hide: {};
  modeChange: { mode: ViewMode };
  message: { content: string; sender: 'user' | 'agent'; timestamp: Date };
  sendMessage: { message: string };
  clearChat: {};
  configUpdate: { config: Partial<WidgetConfig> };
  destroy: {};
  error: { error: Error; context?: string };
}

export interface WidgetAPI {
  show(): void;
  hide(): void;
  setMode(mode: ViewMode): void;
  sendMessage(message: string): void;
  clearChat(): void;
  updateConfig(config: Partial<WidgetConfig>): void;
  destroy(): void;
  on<T extends WidgetEventType>(event: T, callback: (data: WidgetEventData[T]) => void): void;
  off<T extends WidgetEventType>(event: T, callback: (data: WidgetEventData[T]) => void): void;
}

export interface WidgetProps {
  config: WidgetConfig;
  onEvent: (event: WidgetEventType, data: any) => void;
}
