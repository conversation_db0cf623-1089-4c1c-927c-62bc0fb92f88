// API related types and interfaces for the widget

// Base API Response
export interface ApiResponse<T = unknown> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

// Error Response
export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
}

// Send Message API
export interface SendMessagePayload {
  message: string;
  accountId: string;
  customerId: string;
  threadId?: string; // Optional threadId for conversation continuity
  conversationId?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
  stream?: boolean; // Enable streaming response
}

export interface SendMessageApiResponse {
  message: string; // The actual response message from the API
  threadId: string; // Thread ID for conversation continuity
  images?: string[]; // Optional array of base64-encoded images
  messageId?: string; // Optional message ID
  conversationId?: string; // Optional conversation ID
  timestamp?: string; // Optional timestamp
  metadata?: Record<string, unknown>;
}

// Streaming-specific types
export interface StreamingChunk {
  type: 'text' | 'image' | 'tool_call' | 'thread_id' | 'response_start' | 'response_end';
  content: string;
  metadata?: Record<string, unknown>;
}

export interface StreamingState {
  streamedText: string;
  isReceivingImage: boolean;
  imageBuffer: string;
  pendingImages: string[];
  hasReceivedFirstContent: boolean;
  currentTool: string;
  responseStarted: boolean;
  hasReceivedFirstToolCall: boolean;
}

// HTTP Client Configuration
export interface HttpClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers: Record<string, string>;
}

// Request Configuration
export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: unknown;
  params?: Record<string, unknown>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// API Client Interface
export interface ApiClient {
  get<T = unknown>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  post<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  put<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  delete<T = unknown>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  patch<T = unknown>(url: string, data?: unknown, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
}

// Chat Service Interface - Simplified for basic messaging
export interface ChatService {
  sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse>;
  sendStreamingMessage?(payload: SendMessagePayload): Promise<Response>;
}

// Streaming Service Interface
export interface StreamingService {
  processStreamingResponse(response: Response): AsyncGenerator<StreamingChunk, void, unknown>;
  handleStreamingMessage(payload: SendMessagePayload): Promise<Response>;
}

// Streaming Configuration
export interface StreamingConfig {
  enabled: boolean;
  chunkSize?: number;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

// Query Keys for TanStack Query
export const QUERY_KEYS = {
  MESSAGES: (conversationId: string) => ['messages', conversationId] as const,
  CONVERSATIONS: (userId?: string) => ['conversations', userId] as const,
  HEALTH: () => ['health'] as const,
} as const;

// Mutation Keys for TanStack Query
export const MUTATION_KEYS = {
  SEND_MESSAGE: 'sendMessage',
  CREATE_CONVERSATION: 'createConversation',
  DELETE_CONVERSATION: 'deleteConversation',
} as const;

// Default API Configuration
export const DEFAULT_API_CONFIG: HttpClientConfig = {
  baseURL: 'https://localhost:55018', // Default fallback
  timeout: 10000000,
  retries: 0,
  retryDelay: 25000000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

// Error Codes
export const API_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVER_ERROR: 'SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];
