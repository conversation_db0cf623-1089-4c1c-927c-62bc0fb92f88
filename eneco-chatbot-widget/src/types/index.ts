// Export all types from the widget types directory

// Widget types
export * from './widget';

// Chat types (excluding ViewMode to avoid conflict)
export type {
  MessageSender,
  MessageStatus,
  ThemeMode,
  ChatMessage,
  SimpleMessage,
  ChatState,
  MessageFormData,
  BaseComponentProps,
  MessageBubbleProps,
  MessageInputProps,
  ChatContainerProps,
  FloatingChatButtonProps,
  ChatStore
} from './chat';
export { MESSAGE_STATUS, VIEW_MODE, THEME_MODE } from './chat';

// API types
export * from './api';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type Nullable<T> = T | null;

export type Maybe<T> = T | undefined;

export type NonEmptyArray<T> = [T, ...T[]];

export type ValueOf<T> = T[keyof T];

// Event handler types
export type EventHandler<T = Event> = (event: T) => void;

export type ChangeEventHandler = EventHandler<Event>;

export type ClickEventHandler = EventHandler<MouseEvent>;

export type KeyboardEventHandler = EventHandler<KeyboardEvent>;

export type FormEventHandler = EventHandler<Event>;

// Component types
export type ComponentSize = 'sm' | 'md' | 'lg';

export type ComponentVariant = 'primary' | 'secondary' | 'ghost' | 'danger';

export type ComponentState = 'default' | 'loading' | 'disabled' | 'error';

// Animation types
export type AnimationDuration = 'fast' | 'normal' | 'slow';

export type AnimationEasing = 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';

// Color types
export type ColorScheme = 'light' | 'dark';

export type ColorVariant = 
  | 'primary' 
  | 'secondary' 
  | 'success' 
  | 'warning' 
  | 'error' 
  | 'info' 
  | 'neutral';

// Status types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T, E = Error> {
  data: T | null;
  loading: boolean;
  error: E | null;
}

// Accessibility types
export type AriaRole = 
  | 'button'
  | 'dialog'
  | 'textbox'
  | 'listbox'
  | 'option'
  | 'menu'
  | 'menuitem'
  | 'tab'
  | 'tabpanel'
  | 'alert'
  | 'status'
  | 'log';

export interface AriaAttributes {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-hidden'?: boolean;
  'aria-disabled'?: boolean;
  'aria-selected'?: boolean;
  'aria-checked'?: boolean;
  'aria-pressed'?: boolean;
  'aria-current'?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  'aria-live'?: 'off' | 'polite' | 'assertive';
  'aria-atomic'?: boolean;
  'aria-busy'?: boolean;
  'aria-controls'?: string;
  'aria-owns'?: string;
  'aria-activedescendant'?: string;
  role?: AriaRole;
}
