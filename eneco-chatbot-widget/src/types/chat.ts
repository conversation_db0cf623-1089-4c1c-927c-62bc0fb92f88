// Core chat types and interfaces for the widget

export type MessageSender = 'user' | 'agent';

export type MessageStatus = 'sending' | 'sent' | 'error';

export type ViewMode = 'fullpage' | 'chatbox' | 'minimized';

export type ThemeMode = 'light' | 'dark' | 'system';

// Chat Message Interface
export interface ChatMessage {
  id: string;
  content: string;
  sender: MessageSender;
  timestamp: Date;
  status: MessageStatus;
  error?: string; // Error message if status is 'error'
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  images?: string[]; // Optional array of base64-encoded images
  toolInfo?: string; // Information about tool calls during streaming
}

// Simplified Message Interface for store
export interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  images?: string[]; // Optional array of base64-encoded images
  isStreaming?: boolean; // Whether this message is currently being streamed
  isComplete?: boolean; // Whether streaming is complete for this message
  toolInfo?: string; // Information about tool calls during streaming
  isProcessingImage?: boolean; // Whether an image is currently being processed
}

// Chat State Interface
export interface ChatState {
  messages: SimpleMessage[];
  isTyping: boolean;
  viewMode: ViewMode;
  unreadCount: number;
  currentThreadId: string | null; // Current conversation thread ID
  isChatboxExpanded: boolean; // Whether chatbox is in expanded mode
  streamingEnabled: boolean; // Whether streaming is enabled
  currentStreamingMessageId: string | null; // ID of message currently being streamed
  currentAgent: string | null; // Current active agent ID
}

// Form Types
export interface MessageFormData {
  message: string;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface MessageBubbleProps extends BaseComponentProps {
  message: SimpleMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  onRetry?: (messageId: string) => void;
}

export interface MessageInputProps extends BaseComponentProps {
  onSendMessage: (message: string) => Promise<void>;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
}

export interface ChatContainerProps extends BaseComponentProps {
  viewMode?: ViewMode;
  onViewModeChange?: (mode: ViewMode) => void;
  onMinimize?: () => void;
  onClose?: () => void;
}

export interface FloatingChatButtonProps extends BaseComponentProps {
  onClick: () => void;
  unreadCount?: number;
  isOnline?: boolean;
  isVisible: boolean;
}

// Store Interface
export interface ChatStore extends ChatState {
  // Actions
  addMessage: (content: string, sender: 'user' | 'agent', images?: string[]) => void;
  updateMessage: (id: string, updates: Partial<SimpleMessage>) => void;
  clearMessages: () => void;
  setTyping: (isTyping: boolean, agentId?: string) => void;
  setViewMode: (mode: ViewMode) => void;
  markAsRead: () => void;
  setThreadId: (threadId: string | null) => void;
  clearConversation: () => void; // Clear messages and reset thread ID
  toggleChatboxExpanded: () => void; // Toggle chatbox expanded state
  setChatboxExpanded: (expanded: boolean) => void; // Set chatbox expanded state
  setStreamingEnabled: (enabled: boolean) => void; // Enable/disable streaming
  setCurrentStreamingMessageId: (id: string | null) => void; // Set current streaming message ID
  setCurrentAgent: (agentId: string | null) => void; // Set current active agent
}

// Constants
export const MESSAGE_STATUS = {
  SENDING: 'sending' as const,
  SENT: 'sent' as const,
  ERROR: 'error' as const,
} as const;

export const VIEW_MODE = {
  FULLPAGE: 'fullpage' as const,
  CHATBOX: 'chatbox' as const,
  MINIMIZED: 'minimized' as const,
} as const;

export const THEME_MODE = {
  LIGHT: 'light' as const,
  DARK: 'dark' as const,
  SYSTEM: 'system' as const,
} as const;
