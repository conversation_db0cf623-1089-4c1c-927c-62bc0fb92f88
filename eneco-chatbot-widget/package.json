{"name": "eneco-chatbot-widget", "version": "1.0.0", "description": "Standalone Eneco chatbot widget for script tag integration", "type": "module", "main": "dist/eneco-chatbot-widget.iife.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:watch": "vite build --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "highlight.js": "^11.11.1", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "sass": "^1.89.2", "typescript": "~5.7.2", "vite": "^6.0.5"}, "keywords": ["chatbot", "widget", "eneco", "customer-service", "ai-assistant"], "author": "Eneco", "license": "MIT"}