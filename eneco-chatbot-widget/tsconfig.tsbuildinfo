{"root": ["./src/index.ts", "./src/widget.tsx", "./src/components/chatwidget.tsx", "./src/components/chat/chatcontainer.tsx", "./src/components/chat/floatingchatbutton.tsx", "./src/components/chat/markdownrenderer.tsx", "./src/components/chat/messagebubble.tsx", "./src/components/chat/messageinput.tsx", "./src/components/chat/messagelist.tsx", "./src/components/chat/typingindicator.tsx", "./src/hooks/usestreaming.ts", "./src/hooks/api/usechatapi.ts", "./src/services/chatservice.ts", "./src/services/streamingservice.ts", "./src/store/chatstore.ts", "./src/types/api.ts", "./src/types/assets.d.ts", "./src/types/chat.ts", "./src/types/index.ts", "./src/types/widget.ts", "./src/utils/api.ts"], "version": "5.7.3"}